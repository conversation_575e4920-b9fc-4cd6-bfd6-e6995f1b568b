/**
 * 移动端视口高度管理工具
 * 解决移动端浏览器底部导航栏遮挡页面内容的问题
 */

// 视口高度信息接口
export interface ViewportInfo {
  /** 实际可视高度（不包含浏览器UI） */
  visualHeight: number
  /** 窗口高度（包含浏览器UI） */
  windowHeight: number
  /** 文档高度 */
  documentHeight: number
  /** 是否为移动端 */
  isMobile: boolean
  /** 安全区域信息 */
  safeArea: {
    top: number
    bottom: number
    left: number
    right: number
  }
}

// 视口变化监听器类型
export type ViewportChangeListener = (info: ViewportInfo) => void

class ViewportManager {
  private listeners: ViewportChangeListener[] = []
  private currentInfo: ViewportInfo | null = null
  private resizeObserver: ResizeObserver | null = null
  private visualViewport: VisualViewport | null = null

  constructor() {
    this.init()
  }

  private init() {
    if (typeof window === 'undefined') return

    // 初始化视觉视口监听
    this.visualViewport = window.visualViewport || null
    
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize.bind(this))
    window.addEventListener('orientationchange', this.handleOrientationChange.bind(this))
    
    // 监听视觉视口变化（支持的浏览器）
    if (this.visualViewport) {
      this.visualViewport.addEventListener('resize', this.handleVisualViewportResize.bind(this))
      this.visualViewport.addEventListener('scroll', this.handleVisualViewportScroll.bind(this))
    }

    // 初始化CSS自定义属性
    this.updateCSSCustomProperties()
    
    // 立即获取一次视口信息
    this.updateViewportInfo()
  }

  /**
   * 获取当前视口信息
   */
  public getViewportInfo(): ViewportInfo {
    const isMobile = this.isMobileDevice()
    const windowHeight = window.innerHeight
    const documentHeight = document.documentElement.clientHeight
    
    // 获取实际可视高度
    let visualHeight = windowHeight
    if (this.visualViewport) {
      visualHeight = this.visualViewport.height
    } else if (isMobile) {
      // 移动端浏览器地址栏和底部导航栏的估算高度
      const estimatedUIHeight = this.getEstimatedUIHeight()
      visualHeight = Math.max(windowHeight - estimatedUIHeight, windowHeight * 0.7)
    }

    // 获取安全区域信息
    const safeArea = this.getSafeAreaInsets()

    const info: ViewportInfo = {
      visualHeight,
      windowHeight,
      documentHeight,
      isMobile,
      safeArea
    }

    this.currentInfo = info
    return info
  }

  /**
   * 获取安全区域边距
   */
  private getSafeAreaInsets() {
    const computedStyle = getComputedStyle(document.documentElement)
    
    return {
      top: this.parseCSSValue(computedStyle.getPropertyValue('--safe-area-inset-top') || 
                             getComputedStyle(document.body).getPropertyValue('env(safe-area-inset-top)') || '0px'),
      bottom: this.parseCSSValue(computedStyle.getPropertyValue('--safe-area-inset-bottom') || 
                                getComputedStyle(document.body).getPropertyValue('env(safe-area-inset-bottom)') || '0px'),
      left: this.parseCSSValue(computedStyle.getPropertyValue('--safe-area-inset-left') || 
                              getComputedStyle(document.body).getPropertyValue('env(safe-area-inset-left)') || '0px'),
      right: this.parseCSSValue(computedStyle.getPropertyValue('--safe-area-inset-right') || 
                               getComputedStyle(document.body).getPropertyValue('env(safe-area-inset-right)') || '0px')
    }
  }

  /**
   * 解析CSS值为数字
   */
  private parseCSSValue(value: string): number {
    const match = value.match(/(\d+(?:\.\d+)?)/);
    return match ? parseFloat(match[1]) : 0;
  }

  /**
   * 估算移动端浏览器UI高度
   */
  private getEstimatedUIHeight(): number {
    const userAgent = navigator.userAgent.toLowerCase()
    
    // iOS Safari
    if (userAgent.includes('safari') && userAgent.includes('mobile')) {
      return 140 // Safari地址栏 + 底部导航栏
    }
    
    // Android Chrome
    if (userAgent.includes('chrome') && userAgent.includes('mobile')) {
      return 120 // Chrome地址栏 + 底部导航栏
    }
    
    // 其他移动端浏览器
    return 100
  }

  /**
   * 检测是否为移动端设备
   */
  private isMobileDevice(): boolean {
    return window.innerWidth < 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  }

  /**
   * 更新CSS自定义属性
   */
  private updateCSSCustomProperties() {
    const info = this.getViewportInfo()
    const root = document.documentElement
    
    // 设置动态视口高度
    root.style.setProperty('--vh', `${info.visualHeight * 0.01}px`)
    root.style.setProperty('--window-height', `${info.windowHeight}px`)
    root.style.setProperty('--visual-height', `${info.visualHeight}px`)
    root.style.setProperty('--document-height', `${info.documentHeight}px`)
    
    // 设置安全区域
    root.style.setProperty('--safe-area-top', `${info.safeArea.top}px`)
    root.style.setProperty('--safe-area-bottom', `${info.safeArea.bottom}px`)
    root.style.setProperty('--safe-area-left', `${info.safeArea.left}px`)
    root.style.setProperty('--safe-area-right', `${info.safeArea.right}px`)
    
    // 计算可用内容高度（减去导航栏高度）
    const headerHeight = 60 // 顶部导航栏高度
    const bottomNavHeight = 70 // 底部导航栏高度
    const availableHeight = info.visualHeight - headerHeight - bottomNavHeight - info.safeArea.top - info.safeArea.bottom
    root.style.setProperty('--available-height', `${Math.max(availableHeight, 200)}px`)
  }

  /**
   * 处理窗口大小变化
   */
  private handleResize() {
    // 延迟更新，避免频繁触发
    setTimeout(() => {
      this.updateViewportInfo()
    }, 100)
  }

  /**
   * 处理屏幕方向变化
   */
  private handleOrientationChange() {
    // 方向变化后延迟更新
    setTimeout(() => {
      this.updateViewportInfo()
    }, 300)
  }

  /**
   * 处理视觉视口大小变化
   */
  private handleVisualViewportResize() {
    this.updateViewportInfo()
  }

  /**
   * 处理视觉视口滚动
   */
  private handleVisualViewportScroll() {
    // 视觉视口滚动时可能需要更新布局
    this.updateViewportInfo()
  }

  /**
   * 更新视口信息并通知监听器
   */
  private updateViewportInfo() {
    const newInfo = this.getViewportInfo()
    this.updateCSSCustomProperties()
    
    // 通知所有监听器
    this.listeners.forEach(listener => {
      try {
        listener(newInfo)
      } catch (error) {
        console.error('视口变化监听器执行错误:', error)
      }
    })
  }

  /**
   * 添加视口变化监听器
   */
  public addListener(listener: ViewportChangeListener) {
    this.listeners.push(listener)
    
    // 立即执行一次回调
    if (this.currentInfo) {
      listener(this.currentInfo)
    }
  }

  /**
   * 移除视口变化监听器
   */
  public removeListener(listener: ViewportChangeListener) {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  /**
   * 获取动态视口高度（替代100vh）
   */
  public getDynamicVH(): string {
    const info = this.getViewportInfo()
    return `${info.visualHeight}px`
  }

  /**
   * 获取安全的内容高度
   */
  public getSafeContentHeight(excludeHeader = true, excludeBottomNav = true): string {
    const info = this.getViewportInfo()
    let height = info.visualHeight - info.safeArea.top - info.safeArea.bottom
    
    if (excludeHeader) height -= 60 // 顶部导航栏高度
    if (excludeBottomNav) height -= 70 // 底部导航栏高度
    
    return `${Math.max(height, 200)}px`
  }

  /**
   * 销毁管理器
   */
  public destroy() {
    if (typeof window === 'undefined') return
    
    window.removeEventListener('resize', this.handleResize.bind(this))
    window.removeEventListener('orientationchange', this.handleOrientationChange.bind(this))
    
    if (this.visualViewport) {
      this.visualViewport.removeEventListener('resize', this.handleVisualViewportResize.bind(this))
      this.visualViewport.removeEventListener('scroll', this.handleVisualViewportScroll.bind(this))
    }
    
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }
    
    this.listeners = []
    this.currentInfo = null
  }
}

// 创建全局实例
export const viewportManager = new ViewportManager()

// 导出便捷函数
export const getViewportInfo = () => viewportManager.getViewportInfo()
export const getDynamicVH = () => viewportManager.getDynamicVH()
export const getSafeContentHeight = (excludeHeader?: boolean, excludeBottomNav?: boolean) => 
  viewportManager.getSafeContentHeight(excludeHeader, excludeBottomNav)
export const addViewportListener = (listener: ViewportChangeListener) => viewportManager.addListener(listener)
export const removeViewportListener = (listener: ViewportChangeListener) => viewportManager.removeListener(listener)
