/**
 * Vue 3 组合式函数 - 视口管理
 * 提供响应式的视口信息和动态高度计算
 */

import { ref, computed, onMounted, onUnmounted, type Ref } from 'vue'
import { 
  addViewportListener, 
  removeViewportListener, 
  getViewportInfo,
  getDynamicVH,
  getSafeContentHeight,
  type ViewportInfo,
  type ViewportChangeListener 
} from '@/utils/viewport'

export interface UseViewportOptions {
  /** 是否立即获取视口信息 */
  immediate?: boolean
  /** 顶部导航栏高度 */
  headerHeight?: number
  /** 底部导航栏高度 */
  bottomNavHeight?: number
  /** 是否启用调试模式 */
  debug?: boolean
}

export interface UseViewportReturn {
  /** 当前视口信息 */
  viewportInfo: Ref<ViewportInfo | null>
  /** 是否为移动端设备 */
  isMobile: Ref<boolean>
  /** 动态视口高度（替代100vh） */
  dynamicVH: Ref<string>
  /** 安全内容区域高度 */
  safeContentHeight: Ref<string>
  /** 可用内容高度（减去导航栏） */
  availableHeight: Ref<string>
  /** 安全区域信息 */
  safeArea: Ref<{
    top: number
    bottom: number
    left: number
    right: number
  }>
  /** CSS自定义属性对象 */
  cssVars: Ref<Record<string, string>>
  /** 刷新视口信息 */
  refresh: () => void
  /** 获取指定高度的CSS值 */
  getHeightCSS: (excludeHeader?: boolean, excludeBottomNav?: boolean) => string
}

/**
 * 使用视口管理的组合式函数
 */
export function useViewport(options: UseViewportOptions = {}): UseViewportReturn {
  const {
    immediate = true,
    headerHeight = 60,
    bottomNavHeight = 70,
    debug = false
  } = options

  // 响应式数据
  const viewportInfo = ref<ViewportInfo | null>(null)
  
  // 计算属性
  const isMobile = computed(() => viewportInfo.value?.isMobile ?? false)
  
  const dynamicVH = computed(() => {
    if (!viewportInfo.value) return '100vh'
    return `${viewportInfo.value.visualHeight}px`
  })
  
  const safeArea = computed(() => {
    return viewportInfo.value?.safeArea ?? { top: 0, bottom: 0, left: 0, right: 0 }
  })
  
  const safeContentHeight = computed(() => {
    if (!viewportInfo.value) return 'calc(100vh - 130px)'
    
    const totalHeight = viewportInfo.value.visualHeight
    const usedHeight = headerHeight + bottomNavHeight + safeArea.value.top + safeArea.value.bottom
    const availableHeight = Math.max(totalHeight - usedHeight, 200)
    
    return `${availableHeight}px`
  })
  
  const availableHeight = computed(() => {
    if (!viewportInfo.value) return 'calc(100vh - 130px)'
    
    const totalHeight = viewportInfo.value.visualHeight
    const usedHeight = safeArea.value.top + safeArea.value.bottom
    const availableHeight = Math.max(totalHeight - usedHeight, 300)
    
    return `${availableHeight}px`
  })
  
  const cssVars = computed(() => {
    if (!viewportInfo.value) {
      return {
        '--dynamic-vh': '100vh',
        '--safe-content-height': 'calc(100vh - 130px)',
        '--available-height': 'calc(100vh - 60px)',
        '--safe-area-top': '0px',
        '--safe-area-bottom': '0px',
        '--safe-area-left': '0px',
        '--safe-area-right': '0px'
      }
    }
    
    const info = viewportInfo.value
    return {
      '--dynamic-vh': `${info.visualHeight}px`,
      '--window-height': `${info.windowHeight}px`,
      '--document-height': `${info.documentHeight}px`,
      '--safe-content-height': safeContentHeight.value,
      '--available-height': availableHeight.value,
      '--safe-area-top': `${info.safeArea.top}px`,
      '--safe-area-bottom': `${info.safeArea.bottom}px`,
      '--safe-area-left': `${info.safeArea.left}px`,
      '--safe-area-right': `${info.safeArea.right}px`,
      '--header-height': `${headerHeight}px`,
      '--bottom-nav-height': `${bottomNavHeight}px`
    }
  })

  // 视口变化处理函数
  const handleViewportChange: ViewportChangeListener = (info) => {
    viewportInfo.value = info
    
    if (debug) {
      console.log('🔍 视口信息更新:', {
        visualHeight: info.visualHeight,
        windowHeight: info.windowHeight,
        isMobile: info.isMobile,
        safeArea: info.safeArea
      })
    }
    
    // 更新CSS自定义属性
    updateCSSVars()
  }

  // 更新CSS自定义属性到根元素
  const updateCSSVars = () => {
    if (typeof document === 'undefined') return
    
    const root = document.documentElement
    const vars = cssVars.value
    
    Object.entries(vars).forEach(([key, value]) => {
      root.style.setProperty(key, value)
    })
    
    if (debug) {
      console.log('🎨 CSS变量已更新:', vars)
    }
  }

  // 刷新视口信息
  const refresh = () => {
    const info = getViewportInfo()
    handleViewportChange(info)
  }

  // 获取指定配置的高度CSS值
  const getHeightCSS = (excludeHeader = true, excludeBottomNav = true): string => {
    if (!viewportInfo.value) {
      let calc = 'var(--dynamic-vh, 100vh)'
      if (excludeHeader) calc += ` - ${headerHeight}px`
      if (excludeBottomNav) calc += ` - ${bottomNavHeight}px`
      calc += ' - env(safe-area-inset-top) - env(safe-area-inset-bottom)'
      return `calc(${calc})`
    }
    
    const info = viewportInfo.value
    let height = info.visualHeight - info.safeArea.top - info.safeArea.bottom
    
    if (excludeHeader) height -= headerHeight
    if (excludeBottomNav) height -= bottomNavHeight
    
    return `${Math.max(height, 200)}px`
  }

  // 生命周期管理
  onMounted(() => {
    // 添加视口变化监听器
    addViewportListener(handleViewportChange)
    
    // 立即获取视口信息
    if (immediate) {
      refresh()
    }
    
    if (debug) {
      console.log('📱 视口管理已启动')
    }
  })

  onUnmounted(() => {
    // 移除视口变化监听器
    removeViewportListener(handleViewportChange)
    
    if (debug) {
      console.log('📱 视口管理已停止')
    }
  })

  return {
    viewportInfo,
    isMobile,
    dynamicVH,
    safeContentHeight,
    availableHeight,
    safeArea,
    cssVars,
    refresh,
    getHeightCSS
  }
}

/**
 * 简化版本的视口管理Hook，只返回基本信息
 */
export function useSimpleViewport() {
  const { viewportInfo, isMobile, dynamicVH, safeArea } = useViewport({ immediate: true })
  
  return {
    viewportInfo,
    isMobile,
    dynamicVH,
    safeArea
  }
}

/**
 * 获取移动端安全高度的工具函数
 */
export function useSafeHeight(headerHeight = 60, bottomNavHeight = 70) {
  const { getHeightCSS } = useViewport({ headerHeight, bottomNavHeight })
  
  return {
    /** 完整内容高度（包含导航栏） */
    fullHeight: getHeightCSS(false, false),
    /** 主内容高度（排除顶部导航栏） */
    mainHeight: getHeightCSS(true, false),
    /** 可用内容高度（排除顶部和底部导航栏） */
    contentHeight: getHeightCSS(true, true)
  }
}

/**
 * 移动端布局专用Hook
 */
export function useMobileLayout() {
  const viewport = useViewport({
    immediate: true,
    headerHeight: 60,
    bottomNavHeight: 70,
    debug: process.env.NODE_ENV === 'development'
  })
  
  // 移动端专用的计算属性
  const layoutStyle = computed(() => ({
    height: viewport.dynamicVH.value,
    minHeight: viewport.dynamicVH.value
  }))
  
  const headerStyle = computed(() => ({
    paddingTop: `calc(12px + ${viewport.safeArea.value.top}px)`
  }))
  
  const mainStyle = computed(() => ({
    marginTop: `calc(60px + ${viewport.safeArea.value.top}px)`,
    minHeight: viewport.safeContentHeight.value,
    paddingBottom: `${viewport.safeArea.value.bottom}px`
  }))
  
  const bottomNavStyle = computed(() => ({
    bottom: `${viewport.safeArea.value.bottom}px`,
    paddingBottom: `${viewport.safeArea.value.bottom}px`
  }))
  
  return {
    ...viewport,
    layoutStyle,
    headerStyle,
    mainStyle,
    bottomNavStyle
  }
}
