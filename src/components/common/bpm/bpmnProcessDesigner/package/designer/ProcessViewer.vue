<template>
  <div :class="mobileAdapter.getContainerClasses.value">
    <div class="my-process-designer__container" style="background-color: white">
      <div
        :class="mobileAdapter.getCanvasClasses.value"
        :style="mobileAdapter.getCanvasStyle.value"
        ref="bpmnCanvas"
      ></div>
    </div>

    <!-- 缩放控制按钮 - 仅桌面端显示 -->
    <div
      :class="{
        'zoom-controls':!isMobileDevice,
        'zoom-controls-mobile':isMobileDevice,
      }"
      

    >
      <el-button size="small" @click="interactionController.processZoomIn()" class="zoom-btn naive-style">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="zoom-icon"
        >
          <circle cx="11" cy="11" r="8"></circle>
          <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          <line x1="11" y1="8" x2="11" y2="14"></line>
          <line x1="8" y1="11" x2="14" y2="11"></line>
        </svg>
        放大
      </el-button>
      <el-button size="small" class="zoom-btn zoom-value naive-style">
        {{ Math.floor(interactionController.defaultZoom.value * 100) + '%' }}
      </el-button>
      <el-button size="small" @click="interactionController.processZoomOut()" class="zoom-btn naive-style">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="zoom-icon"
        >
          <circle cx="11" cy="11" r="8"></circle>
          <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          <line x1="8" y1="11" x2="14" y2="11"></line>
        </svg>
        缩小
      </el-button>
      <el-button
        type="default"
        size="small"
        @click="interactionController.processReZoom()"
        class="zoom-btn naive-style"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="zoom-icon"
        >
          <polyline points="1 4 1 10 7 10"></polyline>
          <polyline points="23 20 23 14 17 14"></polyline>
          <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
        </svg>
        重置
      </el-button>
      <el-button
        type="success"
        size="small"
        @click="interactionController.downloadDiagram()"
        class="zoom-btn naive-style"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="zoom-icon"
        >
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
          <polyline points="7 10 12 15 17 10"></polyline>
          <line x1="12" y1="15" x2="12" y2="3"></line>
        </svg>
        下载图片
      </el-button>

      <el-button
        type="danger"
        size="small"
        @click="()=>$emit('close')"
        class="zoom-btn naive-style"
      >

        关闭
      </el-button>
    </div>

    <!-- 多签类型图例说明 -->
    <div v-if="highlightManager.showMultiSignLegend.value" class="multi-sign-legend">
      <div class="legend-header">
        <div class="legend-title">多签节点说明：</div>
        <div class="legend-close" @click="highlightManager.closeMultiSignLegend">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </div>
      </div>
      <div class="legend-item sequential">
        <div class="legend-icon"></div>
        <div class="legend-text">串行多签：按顺序依次审批</div>
      </div>
      <div class="legend-item and">
        <div class="legend-icon"></div>
        <div class="legend-text">会签：所有人都需要审批通过</div>
      </div>
      <div class="legend-item or">
        <div class="legend-icon"></div>
        <div class="legend-text">或签：任一人审批即可</div>
      </div>
      <div class="legend-item parallel">
        <div class="legend-icon"></div>
        <div class="legend-text">并行多签：同时处理</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { inject, provide, watch, type Ref } from 'vue'
  import { useBpmnViewer } from './composables/useBpmnViewer'

  defineOptions({ name: 'MyProcessViewer' })

  const props = defineProps({
    value: {
      // BPMN XML 字符串
      type: String,
      default: '',
    },
    prefix: {
      // 使用哪个引擎
      type: String,
      default: 'camunda',
    },
    activityData: {
      // 活动的数据。传递时，可高亮流程
      type: Array,
      default: () => [],
    },
    processInstanceData: {
      // 流程实例的数据。传递时，可展示流程发起人等信息
      type: Object,
      default: () => {},
    },
    taskData: {
      // 任务实例的数据。传递时，可展示 UserTask 审核相关的信息
      type: Array,
      default: () => [],
    },
  })

  provide('configGlobal', props)

  // 监听props变化用于调试
  watch(props, newVal => {
    console.log('Props updated:', newVal)
  })

  const isMobileDevice = inject<Ref<Boolean>>('isMobileDevice')
  const emit = defineEmits(['destroy', 'nextTasks','close'])

  // 使用重构后的BPMN查看器组合式函数
  const { bpmnCanvas, highlightManager, interactionController, overlayManager, mobileAdapter } = useBpmnViewer(
    isMobileDevice!,
    props,
    emit
  )
</script>

<style lang="scss">
  @import './index.scss';

</style>
