/** 处理中 */
.highlight-todo.djs-connection > .djs-visual > path {
  stroke: #1890ff !important;
  stroke-dasharray: 4px !important;
  fill-opacity: 0.2 !important;
}

.highlight-todo.djs-shape .djs-visual > :nth-child(1) {
  fill: #1890ff !important;
  stroke: #1890ff !important;
  stroke-dasharray: 4px !important;
  fill-opacity: 0.2 !important;
}

:deep(.highlight-todo.djs-connection > .djs-visual > path) {
  stroke: #1890ff !important;
  stroke-dasharray: 4px !important;
  fill-opacity: 0.2 !important;
  marker-end: url('#sequenceflow-end-_E7DFDF-_E7DFDF-803g1kf6zwzmcig1y2ulm5egr');
}

:deep(.highlight-todo.djs-shape .djs-visual > :nth-child(1)) {
  fill: #1890ff !important;
  stroke: #1890ff !important;
  stroke-dasharray: 4px !important;
  fill-opacity: 0.2 !important;
}

/** 通过 */
.highlight.djs-shape .djs-visual > :nth-child(1) {
  fill: green !important;
  stroke: green !important;
  fill-opacity: 0.2 !important;
}

.highlight.djs-shape .djs-visual > :nth-child(2) {
  fill: green !important;
}

.highlight.djs-shape .djs-visual > path {
  fill: green !important;
  fill-opacity: 0.2 !important;
  stroke: green !important;
}

.highlight.djs-connection > .djs-visual > path {
  stroke: green !important;
}

.highlight:not(.djs-connection) .djs-visual > :nth-child(1) {
  fill: green !important; /* color elements as green */
}

:deep(.highlight.djs-shape .djs-visual > :nth-child(1)) {
  fill: green !important;
  stroke: green !important;
  fill-opacity: 0.2 !important;
}

:deep(.highlight.djs-shape .djs-visual > :nth-child(2)) {
  fill: green !important;
}

:deep(.highlight.djs-shape .djs-visual > path) {
  fill: green !important;
  fill-opacity: 0.2 !important;
  stroke: green !important;
}

:deep(.highlight.djs-connection > .djs-visual > path) {
  stroke: green !important;
}

.djs-element.highlight > .djs-visual > path {
  stroke: green !important;
}

/** 不通过 */
.highlight-reject.djs-shape .djs-visual > :nth-child(1) {
  fill: red !important;
  stroke: red !important;
  fill-opacity: 0.2 !important;
}

.highlight-reject.djs-shape .djs-visual > :nth-child(2) {
  fill: red !important;
}

.highlight-reject.djs-shape .djs-visual > path {
  fill: red !important;
  fill-opacity: 0.2 !important;
  stroke: red !important;
}

.highlight-reject.djs-connection > .djs-visual > path {
  stroke: red !important;
  marker-end: url(#sequenceflow-end-white-success) !important;
}

.highlight-reject:not(.djs-connection) .djs-visual > :nth-child(1) {
  fill: red !important; /* color elements as green */
}

:deep(.highlight-reject.djs-shape .djs-visual > :nth-child(1)) {
  fill: red !important;
  stroke: red !important;
  fill-opacity: 0.2 !important;
}

:deep(.highlight-reject.djs-shape .djs-visual > :nth-child(2)) {
  fill: red !important;
}

:deep(.highlight-reject.djs-shape .djs-visual > path) {
  fill: red !important;
  fill-opacity: 0.2 !important;
  stroke: red !important;
}

:deep(.highlight-reject.djs-connection > .djs-visual > path) {
  stroke: red !important;
}

/** 已取消 */
.highlight-cancel.djs-shape .djs-visual > :nth-child(1) {
  fill: grey !important;
  stroke: grey !important;
  fill-opacity: 0.2 !important;
}

.highlight-cancel.djs-shape .djs-visual > :nth-child(2) {
  fill: grey !important;
}

.highlight-cancel.djs-shape .djs-visual > path {
  fill: grey !important;
  fill-opacity: 0.2 !important;
  stroke: grey !important;
}

.highlight-cancel.djs-connection > .djs-visual > path {
  stroke: grey !important;
}

.highlight-cancel:not(.djs-connection) .djs-visual > :nth-child(1) {
  fill: grey !important; /* color elements as green */
}

:deep(.highlight-cancel.djs-shape .djs-visual > :nth-child(1)) {
  fill: grey !important;
  stroke: grey !important;
  fill-opacity: 0.2 !important;
}

:deep(.highlight-cancel.djs-shape .djs-visual > :nth-child(2)) {
  fill: grey !important;
}

:deep(.highlight-cancel.djs-shape .djs-visual > path) {
  fill: grey !important;
  fill-opacity: 0.2 !important;
  stroke: grey !important;
}

:deep(.highlight-cancel.djs-connection > .djs-visual > path) {
  stroke: grey !important;
}

/** 回退 */
.highlight-return.djs-shape .djs-visual > :nth-child(1) {
  fill: #e6a23c !important;
  stroke: #e6a23c !important;
  fill-opacity: 0.2 !important;
}

.highlight-return.djs-shape .djs-visual > :nth-child(2) {
  fill: #e6a23c !important;
}

.highlight-return.djs-shape .djs-visual > path {
  fill: #e6a23c !important;
  fill-opacity: 0.2 !important;
  stroke: #e6a23c !important;
}

.highlight-return.djs-connection > .djs-visual > path {
  stroke: #e6a23c !important;
}

.highlight-return:not(.djs-connection) .djs-visual > :nth-child(1) {
  fill: #e6a23c !important; /* color elements as green */
}

:deep(.highlight-return.djs-shape .djs-visual > :nth-child(1)) {
  fill: #e6a23c !important;
  stroke: #e6a23c !important;
  fill-opacity: 0.2 !important;
}

:deep(.highlight-return.djs-shape .djs-visual > :nth-child(2)) {
  fill: #e6a23c !important;
}

:deep(.highlight-return.djs-shape .djs-visual > path) {
  fill: #e6a23c !important;
  fill-opacity: 0.2 !important;
  stroke: #e6a23c !important;
}

:deep(.highlight-return.djs-connection > .djs-visual > path) {
  stroke: #e6a23c !important;
}

.element-overlays {
  min-width: 320px;
  max-width: 520px;
  padding: 8px 10px;
  color: #333;
  margin-right: -100px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  font-size: 13px;
  line-height: 1.5;
  transition: all 0.3s ease;
  font-family: 'Helvetica Neue', Arial, sans-serif;
  letter-spacing: 0.3px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  border-color: #d0d0d0;

  p {
    margin-bottom: 2px;

    &:last-child {
      margin-bottom: 0;
    }

    > svg {
      width: 1em !important;
      height: 1em !important;
      margin-right: 2px;
      vertical-align: middle;
    }
  }

  .approver-info {
    padding: 4px 0;

    p {
      padding-left: 4px;
    }
  }

  .node-id {
    margin-top: 5px;
    border-top: 1px dotted #eee;
    padding-top: 5px;
    color: #999;
    font-size: 12px;
  }
}

/* 多签任务分隔线样式 */
.element-overlays div[style*='border-top'] {
  margin: 10px 0 !important;
  border-top: 1px dashed #aaa !important;
}

/* 多签类型样式 */
.multi-sign-type {
  margin-bottom: 10px;
  padding: 8px 10px;
  border-radius: 6px;
  background-color: #f5f7fa;
}

.multi-sign-type .sign-type-label {
  font-weight: bold;
}

.multi-sign-type .sign-progress {
  font-weight: bold;
}

.multi-sign-type.sequential {
  background-color: #e6f7ff;
  border-left: 3px solid #1890ff;
}

.multi-sign-type.and {
  background-color: #f6ffed;
  border-left: 3px solid #52c41a;
}

.multi-sign-type.or {
  background-color: #fff7e6;
  border-left: 3px solid #fa8c16;
}

.multi-sign-type.parallel {
  background-color: #f9f0ff;
  border-left: 3px solid #722ed1;
}

/* 流程图中多签类型节点高亮样式 */
.multi-sequential.djs-shape .djs-visual > :nth-child(1) {
  stroke-dasharray: 4px !important;
  stroke-width: 2px !important;
  stroke: #1890ff !important;
}

.multi-and.djs-shape .djs-visual > :nth-child(1) {
  stroke-dasharray: 0 !important;
  stroke-width: 2px !important;
  stroke: #52c41a !important;
}

.multi-or.djs-shape .djs-visual > :nth-child(1) {
  stroke-dasharray: 6px !important;
  stroke-width: 2px !important;
  stroke: #fa8c16 !important;
}

.multi-parallel.djs-shape .djs-visual > :nth-child(1) {
  stroke-dasharray: 2px !important;
  stroke-width: 2px !important;
  stroke: #722ed1 !important;
}

/* 同时支持深层嵌套的multi标记 */
:deep(.multi-sequential.djs-shape .djs-visual > :nth-child(1)) {
  stroke-dasharray: 4px !important;
  stroke-width: 2px !important;
  stroke: #1890ff !important;
}

:deep(.multi-and.djs-shape .djs-visual > :nth-child(1)) {
  stroke-dasharray: 0 !important;
  stroke-width: 2px !important;
  stroke: #52c41a !important;
}

:deep(.multi-or.djs-shape .djs-visual > :nth-child(1)) {
  stroke-dasharray: 6px !important;
  stroke-width: 2px !important;
  stroke: #fa8c16 !important;
}

:deep(.multi-parallel.djs-shape .djs-visual > :nth-child(1)) {
  stroke-dasharray: 2px !important;
  stroke-width: 2px !important;
  stroke: #722ed1 !important;
}

.preview-icon {
  width: 1em !important;
  height: 1em !important;
}

.my-process-designer {
  position: relative;
  // position: fixed;
  // top: 0;
  // left: 0;
  // width: 100vh;
  // height: 100vw;
  // transform: rotate(90deg);
  // transform-origin: center;
  // margin-left: calc((100vw - 100vh) / 2);
  // margin-top: calc((100vh - 100vw) / 2);
}

.zoom-controls-mobile {
  position: absolute; // 绝对定位,使控件固定在容器内的特定位置
  bottom: 20px; // 距离底部20px
  left: 50%; // 左侧距离50%
  display: flex; // 使用flex布局,让子元素水平排列
  gap: 10px; // 子元素之间的间隔为10px
  transform: translateX(-50%); // 向左平移自身宽度的50%,实现水平居中
  &:hover {
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.12);
  }

  .zoom-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    
    &.naive-style {
      border-radius: 3px;
      font-size: 13px;
      border: 1px solid #e0e0e0;

      &:hover {
        color: #18a058;
        border-color: #18a058;
        background-color: rgba(24, 160, 88, 0.1);
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
      }

      &[type='success'] {
        background-color: #18a058;
        color: white;
        border-color: #18a058;

        &:hover {
          background-color: #36ad6a;
          border-color: #36ad6a;
        }
      }

      &[type='info'] {
        background-color: #2080f0;
        color: white;
        border-color: #2080f0;

        &:hover {
          background-color: #4098fc;
          border-color: #4098fc;
        }
      }
    }

    .zoom-icon {
      margin-right: 4px;
    }
  }

  .zoom-value {
    min-width: 60px;
    font-weight: bold;
    background-color: transparent;
    border: 1px solid #e0e0e0;
    color: #666;
  }
}
.zoom-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 8px;
  padding: 10px;
  background-color: rgba(250, 250, 252, 0.85);
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(4px);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.12);
  }

  .zoom-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &.naive-style {
      border-radius: 3px;
      font-size: 13px;
      border: 1px solid #e0e0e0;

      &:hover {
        color: #18a058;
        border-color: #18a058;
        background-color: rgba(24, 160, 88, 0.1);
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
      }

      &[type='success'] {
        background-color: #18a058;
        color: white;
        border-color: #18a058;

        &:hover {
          background-color: #36ad6a;
          border-color: #36ad6a;
        }
      }

      &[type='info'] {
        background-color: #2080f0;
        color: white;
        border-color: #2080f0;

        &:hover {
          background-color: #4098fc;
          border-color: #4098fc;
        }
      }
    }

    .zoom-icon {
      margin-right: 4px;
    }
  }

  .zoom-value {
    min-width: 60px;
    font-weight: bold;
    background-color: transparent;
    border: 1px solid #e0e0e0;
    color: #666;
  }
}

/* 多签类型图例样式 */
.multi-sign-legend {
  position: absolute;
  bottom: 20px;
  left: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(4px);
  border: 1px solid #e0e0e0;
  font-size: 13px;
  z-index: 100;
}

.legend-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.legend-close {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  transition: all 0.2s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  svg {
    color: #999;
  }
}

.legend-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-icon {
  width: 20px;
  height: 20px;
  border-radius: 3px;
  border: 1px solid #ddd;
}

.legend-item.sequential .legend-icon {
  border: 2px dashed #1890ff;
}

.legend-item.and .legend-icon {
  border: 2px solid #52c41a;
}

.legend-item.or .legend-icon {
  border: 2px dotted #fa8c16;
}

.legend-item.parallel .legend-icon {
  border: 2px dashed #722ed1;
  border-style: dashed double;
}

// 移动端适配样式
.mobile-mode {
  .element-overlays {
    min-width: 240px;
    max-width: 280px;
    font-size: 12px;
    padding: 6px 8px;
    margin-right: -50px;
    
    // 移动端点击后的overlay样式
    &.mobile-clicked {
      animation: mobileOverlayShow 0.2s ease-out;
      border: 2px solid #18a058;
      box-shadow: 0 4px 16px rgba(24, 160, 88, 0.2);
      
      // 添加自动隐藏倒计时提示
      &::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        height: 2px;
        background: linear-gradient(90deg, #18a058 0%, #18a058 100%);
        border-radius: 6px 6px 0 0;
        animation: mobileOverlayTimer 15s linear;
      }
    }
  }

  // 移动端BPMN元素点击态样式
  .djs-element {
    &.mobile-clickable {
      cursor: pointer;
      
      &:active {
        .djs-visual > :first-child {
          filter: brightness(0.95);
          transform: scale(0.98);
          transition: all 0.1s ease;
        }
      }
      
      // 为有信息的节点添加点击提示
      &.has-info {
        .djs-visual > :first-child {
          stroke-width: 2px;
          stroke-dasharray: 3 3;
          stroke: #36ad6a;
          animation: mobileClickHint 2s ease-in-out infinite;
        }
      }
    }
  }

  .multi-sign-legend {
    bottom: 10px;
    left: 10px;
    padding: 8px;
    font-size: 11px;
    min-width: 160px;
  }

  .zoom-controls {
    display: none; // 移动端隐藏缩放控制按钮
  }
}

.mobile-canvas {
  touch-action: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

// 移动端overlay动画
@keyframes mobileOverlayShow {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// 移动端overlay自动隐藏倒计时动画
@keyframes mobileOverlayTimer {
  0% {
    width: 100%;
  }
  100% {
    width: 0%;
  }
}

// 移动端点击提示动画
@keyframes mobileClickHint {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

// 移动端提示文本样式
.mobile-overlay-hint {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(24, 160, 88, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
  z-index: 1000;
  animation: mobileHintFadeIn 0.3s ease-out;
  
  &.fade-out {
    animation: mobileHintFadeOut 0.3s ease-in;
  }
}

@keyframes mobileHintFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(-5px);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes mobileHintFadeOut {
  0% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-5px);
  }
}
