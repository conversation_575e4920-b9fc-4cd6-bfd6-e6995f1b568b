<template>
  <div class="mobile-task-list">
    <!-- 流程结束卡片 -->
    <div v-if="processInstance.endTime" class="timeline-item">
      <div class="timeline-connector" :class="getEndConnectorClass(processInstance.status)"></div>
      <div class="timeline-dot" :class="getEndDotClass(processInstance.status)">
        <!-- 审批通过图标 -->
        <svg v-if="processInstance.status === 2" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
          />
        </svg>
        <!-- 审批不通过图标 -->
        <svg v-else-if="processInstance.status === 3" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm3.5 6L12 10.5 8.5 8 7 9.5 10.5 12 7 14.5 8.5 16 12 13.5 15.5 16 17 14.5 13.5 12 17 9.5 15.5 8z"
          />
        </svg>
        <!-- 已取消图标 -->
        <svg v-else-if="processInstance.status === 4" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 5h-2v6h2V7zm0 8h-2v2h2v-2z"
          />
        </svg>
        <!-- 默认结束图标 -->
        <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
          />
        </svg>
      </div>
      <div class="task-card end-card">
        <div class="card-header" :class="getEndCardHeaderClass(processInstance.status)">
          <div class="status-icon" :class="getEndStatusIconClass(processInstance.status)">
            <!-- 审批通过图标 -->
            <svg v-if="processInstance.status === 2" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
              />
            </svg>
            <!-- 审批不通过图标 -->
            <svg v-else-if="processInstance.status === 3" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm3.5 6L12 10.5 8.5 8 7 9.5 10.5 12 7 14.5 8.5 16 12 13.5 15.5 16 17 14.5 13.5 12 17 9.5 15.5 8z"
              />
            </svg>
            <!-- 已取消图标 -->
            <svg v-else-if="processInstance.status === 4" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 5h-2v6h2V7zm0 8h-2v2h2v-2z"
              />
            </svg>
            <!-- 默认结束图标 -->
            <svg v-else xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
              />
            </svg>
          </div>
          <div class="task-info">
            <div class="task-title" :style="{
              color: getStatusColor(processInstance.status)
            }">
              流程结束 {{ getProcessStatusLabel(processInstance.status) }}
            </div>
            <div class="task-time">{{ formatDate(processInstance?.endTime) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 审批任务卡片 -->
    <div v-for="(item, index) in tasks" :key="index" class="timeline-item">
      <!-- 时间线连接器，除了最后一个任务不显示 -->
      <div
        v-if="index < tasks.length - 1 || !processInstance.endTime"
        class="timeline-connector"
        :class="getConnectorClass(item.status)"
      >
      </div>

      <!-- 时间线节点 -->
      <div class="timeline-dot" :class="getTaskStatusClass(item.status)">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"
          />
        </svg>
      </div>

      <div class="task-card">
        <div class="card-header">
          <div class="status-icon" :class="getTaskStatusClass(item.status)">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"
              />
            </svg>
          </div>
          <div class="task-info">
            <div class="task-title">{{ item.name }}</div>
            <div class="task-time" v-if="item.endTime">{{ formatDate(item?.auditTime ?? item?.endTime) }}</div>
          </div>
          <n-tag :type="getTaskStatusType(item.status)" size="small">
            {{ getTaskStatusLabel(item.status) }}
          </n-tag>
        </div>

        <div class="card-content">
          <!-- 审批人信息 -->
          <div v-if="item.assigneeUser" class="assignee-info">
            <div class="info-row">
              <span class="label">审批人：</span>
              <span class="value">{{ item.assigneeUser.empName }}({{ item.assigneeUser.empCode }})</span>
            </div>
            <div v-if="item.assigneeUser.deptName" class="info-row">
              <span class="label">部门：</span>
              <span class="value">{{ item.assigneeUser.deptName }}</span>
            </div>
          </div>

          <!-- 审批建议 -->
          <div v-if="item.reason" class="reason-info">
            <div class="info-row">
              <span class="label">审批建议：</span>
              <span class="value">{{ item.reason }}</span>
            </div>
          </div>

          <!-- 签名 -->
          <div v-if="item.needSign && item.status != 1 && item.signUrl" class="signature-info">
            <div class="info-row">
              <span class="label">签名：</span>
              <n-image
                :width="60"
                :src="JPGlobal.getRealOCUrl(item.signUrl)"
                :preview-disabled="false"
                class="signature-image"
              />
            </div>
          </div>

          <!-- 附件 -->
          <div v-if="item.needAttachment && item.status != 1 && item.attachmentList?.length" class="attachment-info">
            <div class="info-row">
              <span class="label">附件：</span>
            </div>
            <div class="attachment-list">
              <div
                v-for="attachment in item.attachmentList"
                :key="attachment.value"
                class="attachment-item"
                @click="handleAttachmentClick(attachment)"
              >
                <div class="attachment-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"
                    />
                  </svg>
                </div>
                <span class="attachment-name">{{ attachment.label }}</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <n-button v-if="item.children" size="small" type="primary" ghost @click="openChildrenTask(item)">
              查看子任务
            </n-button>
            <n-button v-if="item.formId > 0" size="small" type="info" ghost @click="handleFormDetail(item)">
              查看表单
            </n-button>
            <n-button
              v-if="item.endTime && runningTasks.length > 0"
              size="small"
              type="warning"
              ghost
              @click="handleTimeEdit(item)"
            >
              修改时间
            </n-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 流程发起卡片 -->
    <div class="timeline-item">
      <!-- 起始连接器 -->
      <div class="timeline-connector start-connector"></div>

      <!-- 起始节点 -->
      <div class="timeline-dot start-dot">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
          <path fill="currentColor" d="M8 5v14l11-7z" />
        </svg>
      </div>

      <div class="task-card start-card">
        <div class="card-header">
          <div class="status-icon start">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24">
              <path fill="currentColor" d="M8 5v14l11-7z" />
            </svg>
          </div>
          <div class="task-info">
            <div class="task-title">发起流程</div>
            <div class="task-time">{{ formatDate(processInstance?.startTime) }}</div>
          </div>
        </div>
        <div class="card-content">
          <div class="info-row">
            <span class="label">发起人：</span>
            <span class="value">{{ processInstance.startUser?.empName }}</span>
          </div>
          <div class="info-row">
            <span class="label">流程名称：</span>
            <span class="value">{{ processInstance.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed } from 'vue'
  import { formatDate } from '@/utils/bpmAdapter/formatTime'
  import { DICT_TYPE, getIntDictOptions } from '@/utils/bpmAdapter/bpmDictAdapter'
  import JPGlobal from '@/types/common/jglobal'

  defineOptions({ name: 'MobileTaskList' })

  const props = defineProps({
    processInstance: {
      type: Object,
      required: true,
    },
    tasks: {
      type: Array<any>,
      default: () => [],
    },
    runningTasks: {
      type: Array,
      default: () => [],
    },
  })

  const emit = defineEmits(['openChildrenTask', 'handleFormDetail', 'handleTimeEdit', 'handleAttachment', 'refresh'])

  // 状态选项
  const taskStatusOptions = getIntDictOptions(DICT_TYPE.BPM_TASK_STATUS)
  const processStatusOptions = getIntDictOptions(DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS)

  // 获取任务状态类型
  const getTaskStatusType = (status: number) => {
    switch (status) {
      case 0:
        return 'info' // 待处理
      case 1:
        return 'warning' // 处理中
      case 2:
        return 'success' // 已通过
      case 3:
        return 'error' // 已拒绝
      case 4:
        return 'default' // 已取消
      case 5:
        return 'warning' // 已退回
      case 6:
        return 'info' // 委派中
      case 7:
        return 'info' // 审批中
      default:
        return 'default'
    }
  }

  // 获取任务状态标签
  const getTaskStatusLabel = (status: number) => {
    const option = taskStatusOptions.find(item => item.value === status)
    return option ? option.label : '未知状态'
  }

  // 获取任务状态样式类
  const getTaskStatusClass = (status: number) => {
    switch (status) {
      case 2:
        return 'success'
      case 3:
        return 'error'
      case 4:
        return 'cancelled'
      case 5:
        return 'warning'
      default:
        return 'pending'
    }
  }

  // 获取连接器样式类
  const getConnectorClass = (status: number) => {
    switch (status) {
      case 2:
        return 'success-connector'
      case 3:
        return 'error-connector'
      case 4:
        return 'cancelled-connector'
      case 5:
        return 'warning-connector'
      default:
        return 'pending-connector'
    }
  }

  const getStatusColor = (status: number) => {
    switch (status) {
      case 2:
        return '#52c41a' // 审批通过 - 绿色
      case 3:
        return '#f5222d' // 审批不通过 - 红色
      case 4:
        return '#faad14' // 已取消 - 黄色
      default:
        return '#1890ff' // 待处理蓝色
    }
  }

  // 获取结束连接器样式类
  const getEndConnectorClass = (status: number) => {
    switch (status) {
      case 2: return 'end-connector success-connector'  // 审批通过 - 绿色
      case 3: return 'end-connector error-connector'  // 审批不通过 - 红色
      case 4: return 'end-connector cancelled-connector'  // 已取消 - 黄色
      default: return 'end-connector success-connector'
    }
  }

  // 获取结束节点样式类
  const getEndDotClass = (status: number) => {
    switch (status) {
      case 2: return 'end-dot success'  // 审批通过 - 绿色
      case 3: return 'end-dot error'  // 审批不通过 - 红色
      case 4: return 'end-dot cancelled'  // 已取消 - 黄色
      default: return 'end-dot success'
    }
  }

  // 获取结束卡片头部样式类
  const getEndCardHeaderClass = (status: number) => {
    switch (status) {
      case 2: return 'end-card-header-success'  // 审批通过 - 绿色
      case 3: return 'end-card-header-error'  // 审批不通过 - 红色
      case 4: return 'end-card-header-cancelled'  // 已取消 - 黄色
      default: return 'end-card-header-success'
    }
  }

  // 获取结束状态图标样式类
  const getEndStatusIconClass = (status: number) => {
    switch (status) {
      case 2: return 'success'  // 审批通过 - 绿色
      case 3: return 'error'  // 审批不通过 - 红色
      case 4: return 'cancelled'  // 已取消 - 黄色
      default: return 'success'
    }
  }

  // 获取流程状态类型
  const getProcessStatusType = (status: number) => {
    switch (status) {
      case 1:
        return 'info' // 进行中
      case 2:
        return 'success' // 已完成
      case 3:
        return 'error' // 已取消
      default:
        return 'default'
    }
  }

  // 获取流程状态标签
  const getProcessStatusLabel = (status: number) => {
    const option = processStatusOptions.find(item => item.value === status)
    return option ? option.label : '未知状态'
  }

  // 事件处理
  const openChildrenTask = (item: any) => {
    emit('openChildrenTask', item)
  }

  const handleFormDetail = (item: any) => {
    emit('handleFormDetail', item)
  }

  const handleTimeEdit = (item: any) => {
    emit('handleTimeEdit', item)
  }

  const handleAttachmentClick = (attachment: any) => {
    emit('handleAttachment', attachment)
  }
</script>

<style scoped>
  .mobile-task-list {
    padding: 10px 0px;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  /* 时间线容器 */
  .timeline-item {
    display: flex;
    align-items: flex-start;
    position: relative;
    margin-bottom: 20px;
  }

  .timeline-item:last-child {
    margin-bottom: 0;
  }

  /* 时间线连接器 */
  .timeline-connector {
    position: absolute;
    left: 20px;
    top: 48px;
    bottom: -20px;
    width: 2px;
    background: #e8e8e8;
    z-index: 1;
  }

  /* 不同状态的连接器颜色 */
  .success-connector {
    background: linear-gradient(to bottom, #52c41a 0%, #73d13d 100%);
  }

  .error-connector {
    background: linear-gradient(to bottom, #ff4d4f 0%, #ff7875 100%);
  }

  .warning-connector {
    background: linear-gradient(to bottom, #faad14 0%, #ffc53d 100%);
  }

  .pending-connector {
    background: linear-gradient(to bottom, #1890ff 0%, #40a9ff 100%);
  }

  .cancelled-connector {
    background: linear-gradient(to bottom, #8c8c8c 0%, #bfbfbf 100%);
  }

  .start-connector {
    background: linear-gradient(to top, #1890ff 0%, #40a9ff 100%);
    top: 0;
    bottom: auto;
    height: 20px;
  }

  .end-connector {
  top: 48px;
}

.end-connector.success-connector {
  background: linear-gradient(to bottom, #52c41a 0%, #73d13d 100%);
}

.end-connector.cancelled-connector {
  background: linear-gradient(to bottom, #faad14 0%, #ffc53d 100%);
}

.end-connector.error-connector {
  background: linear-gradient(to bottom, #ff4d4f 0%, #ff7875 100%);
}

  /* 时间线节点 */
  .timeline-dot {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    position: relative;
    z-index: 2;
    border: 3px solid #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    margin-right: 16px;
  }

  .timeline-dot.success {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    color: white;
  }

  .timeline-dot.error {
    background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
    color: white;
  }

  .timeline-dot.warning {
    background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
    color: white;
  }

  .timeline-dot.pending {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    color: white;
  }

  .timeline-dot.cancelled {
    background: linear-gradient(135deg, #8c8c8c 0%, #bfbfbf 100%);
    color: white;
  }

  .start-dot {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: white;
}

.end-dot.success {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  color: white;
}

.end-dot.cancelled {
  background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
  color: white;
}

.end-dot.error {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  color: white;
}

  /* 任务卡片 */
  .task-card {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;
    overflow: hidden;
    flex: 1;
    position: relative;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  }

  .card-header {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    gap: 12px;
  }

  .status-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .status-icon.success {
    background: #f6ffed;
    color: #52c41a;
  }

  .status-icon.error {
    background: #fff2f0;
    color: #ff4d4f;
  }

  .status-icon.warning {
    background: #fffbe6;
    color: #faad14;
  }

  .status-icon.pending {
    background: #f0f5ff;
    color: #1890ff;
  }

  .status-icon.cancelled {
  background: #fffbe6;
  color: #faad14;
}

  .status-icon.start {
    background: #e6f7ff;
    color: #1890ff;
  }

  .task-info {
    flex: 1;
    min-width: 0;
  }

  .task-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 4px;
    line-height: 1.4;
  }

  .task-time {
    font-size: 14px;
    color: #8c8c8c;
  }

  .card-content {
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .info-row {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    line-height: 1.5;
  }

  .label {
    font-size: 14px;
    color: #8c8c8c;
    flex-shrink: 0;
    min-width: 60px;
  }

  .value {
    font-size: 14px;
    color: #262626;
    flex: 1;
    word-break: break-all;
  }

  .attachment-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 8px;
  }

  .attachment-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .attachment-item:active {
    background: #e9ecef;
  }

  .attachment-icon {
    color: #1890ff;
    flex-shrink: 0;
  }

  .attachment-name {
    font-size: 14px;
    color: #262626;
    flex: 1;
    word-break: break-all;
  }

  .signature-image {
    border-radius: 4px;
    border: 1px solid #f0f0f0;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 8px;
  }

  .action-buttons .n-button {
    flex: 1;
    min-width: 80px;
  }

  /* 特殊卡片样式 */
.end-card-header-success {
  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%) !important;
}

.end-card-header-cancelled {
  background: linear-gradient(135deg, #fffbe6 0%, #fff1b8 100%) !important;
}

.end-card-header-error {
  background: linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%) !important;
}

.start-card .card-header {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
}

  /* 添加时间线流动动画 */
  @keyframes flow {
    0% {
      background-position: 0% 0%;
    }
    100% {
      background-position: 0% 100%;
    }
  }

  .timeline-connector.success-connector,
  .timeline-connector.pending-connector {
    background-size: 100% 20px;
    animation: flow 3s linear infinite;
  }
</style>
