<template>
  <ContentWrap>
    <n-spin :show="runningTaskLoading">
      <div class="tabs-container" :class="{ 'mobile-layout': isMobileDevice }">
        <n-tabs
          :type="isMobileDevice ? 'segment' : 'card'"
          :animated="false"
          v-model:value="activeTab"
          ref="ntabsRef"
          :size="isMobileDevice ? 'large' : 'medium'"
        >
          <!-- Tab 1: 申请信息 -->
          <n-tab-pane name="application" tab="申请信息" display-directive="show">
            <!-- <el-scrollbar height="75vh"> -->
            <el-scrollbar :height="isMobileDevice ? '78vh' : '78vh'">
              <!-- 申请信息 -->
              <card v-loading="processInstanceLoading && !runningTaskLoading">
                <template #header>
                  <span class="el-icon-document">
                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24">
                      <path
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        d="M5 12a3 3 0 1 1 0-6a3 3 0 0 1 0 6Zm4 6v-2c0-2.25-1.787-4-4.036-4h.054C2.768 12 1 13.75 1 16v2M12 7h12M12 17h10m-10-5h7"
                      />
                    </svg>
                    申请信息【{{ processInstance.name }}】</span
                  >
                </template>

                <!-- 情况一：流程表单 -->
                <el-col v-if="[10, 40].includes(processInstance?.processDefinition?.formType)" :offset="3" :span="14">
                  <form-create
                    v-model="detailForm.value"
                    v-model:api="fApi"
                    :option="detailForm.option"
                    :rule="detailForm.rule"
                  />
                </el-col>

                <!-- 情况二：业务表单 -->
                <div v-if="[20, 30].includes(processInstance?.processDefinition?.formType)">
                  <BusinessFormComponent
                    class="businessFormComponent"
                    ref="businessFormComponentRef"
                    :id="processInstance.businessKey"
                    :setHandleAuditBefoerFn="setHandleAuditBefoerFn"
                    :processInstanceVariables="processInstance.formVariables"
                    :runningTasks="runningTasks"
                    :tasks="tasks"
                    :otherProps="otherProps"
                    tech-query
                    ethics-approval
                    application-declaration
                    @close="close"
                  />
                </div>
              </card>

              <!-- 提示 切换到审批操作 -->
              <div>
                <n-alert title="提示" type="info" style="margin-top: 20px" v-if="runningTasks.length > 0">
                  <template #title>
                    <span
                      >切换到

                      {{ runningTasks.length > 0 ? '审批操作' : '流程图' }}
                    </span>
                  </template>

                  <span
                    >点击上方的
                    <span
                      style="color: #409eff; cursor: pointer"
                      @click="
                        () => {
                          // 切换到审批操作
                          activeTabChange(runningTasks.length > 0 ? 'approval' : 'history')
                        }
                      "
                      >{{ runningTasks.length > 0 ? '审批操作' : '流程图' }}</span
                    >
                    标签，切换到{{ runningTasks.length > 0 ? '审批操作' : '流程图' }}页面</span
                  >
                  或者
                  <NButton type="primary" @click="activeTabChange(runningTasks.length > 0 ? 'approval' : 'history')">
                    直接点击这里
                  </NButton>
                </n-alert>
              </div>
            </el-scrollbar>
          </n-tab-pane>

          <!-- Tab 2: 审批操作 -->
          <n-tab-pane name="approval" tab="审批操作" v-if="runningTasks.length > 0" display-directive="show">
            <el-scrollbar :height="isMobileDevice ? '100%' : '80vh'">
              <!-- 审批信息 -->
              <el-card
                v-for="(item, index) in runningTasks"
                :key="index"
                v-loading="processInstanceLoading"
                :class="['box-card', { 'mobile-approval-card': isMobileDevice }]"
                shadow="never"
              >
                <!-- item:{{ item }} -->
                <template #header>
                  <div :class="{ 'mobile-card-header': isMobileDevice }">
                    <span class="el-icon-picture-outline">审批任务【{{ item.name }}】</span>
                  </div>
                </template>
                <div :class="isMobileDevice ? 'mobile-form-container' : 'desktop-form-container'" v-loading="tasksLoad">
                  <el-form
                    :ref="'form' + index"
                    :model="auditForms[index]"
                    :rules="auditRule"
                    :label-width="isMobileDevice ? '100%' : '136px'"
                    :label-position="isMobileDevice ? 'top' : 'right'"
                    :class="{ 'mobile-form': isMobileDevice }"
                  >
                    <el-form-item v-if="processInstance && processInstance.name" label="流程名:">
                      {{ processInstance.name }}
                    </el-form-item>
                    <el-form-item v-if="processInstance && processInstance.startUser" label="流程发起人:">
                      <n-popover trigger="hover" placement="top">
                        <template #trigger>
                          <div>
                            {{ processInstance?.startUser.empName }}
                            <n-tag type="info" style="margin-left: 10px" size="medium">
                              {{ processInstance?.startUser.deptName }}
                            </n-tag>
                          </div>
                        </template>
                        <div>
                          <p>工号：{{ processInstance?.startUser.empCode }}</p>
                          <p>姓名：{{ processInstance?.startUser.empName }}</p>
                          <p>部门名称：{{ processInstance?.startUser.deptName }}</p>
                          <p>部门编码：{{ processInstance?.startUser?.deptCode }}</p>
                        </div>
                      </n-popover>
                    </el-form-item>
                    <div
                      style="
                        margin: 0 auto;
                        border: 1px solid #dcdfe6;
                        border-radius: 4px;
                        padding: 10px;
                        margin-bottom: 10px;
                      "
                      v-if="approveForms[index].rule && Object.keys(approveForms[index].rule).length > 0"
                    >
                      <el-alert
                        title="审批中信息补充：如下一节点审批人不确定就需要在此补充"
                        type="info"
                        :closable="false"
                        style="margin-bottom: 5px; padding-left: 60px"
                      />

                      <FormCreate
                        v-model="approveForms[index].value"
                        v-model:api="approveFormFApis[index]"
                        :option="approveForms[index].option"
                        :rule="approveForms[index].rule"
                      />
                    </div>

                    <el-form-item label="审批建议:" prop="reason">
                      <el-input
                        v-model="auditForms[index].reason"
                        placeholder="请输入审批建议"
                        type="textarea"
                        show-count
                      />
                      <div class="quick-reason-tags">
                        <n-space>
                          <n-tag
                            v-for="(item, i) in reasonOptions"
                            :key="i"
                            size="small"
                            :type="item.type"
                            style="cursor: pointer; margin-right: 5px"
                            @click="selectQuickReason(index, item.value)"
                          >
                            {{ item.label }}
                          </n-tag>
                          <n-text style="color: #409eff; cursor: pointer" size="small">快捷输入 (点击添加)</n-text>
                        </n-space>
                      </div>
                    </el-form-item>
                    <el-form-item label="自选审批时间:" prop="auditTime">
                      <n-date-picker
                        v-model:formatted-value="auditForms[index].auditTime"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        placeholder="不选则默认当前时间"
                        type="datetime"
                        clearable
                      />
                    </el-form-item>
                    <el-form-item label="抄送人:" prop="copyUserIds">
                      <j-bus-emp-search v-model:value="auditForms[index].copyUserIds" multiple />
                    </el-form-item>
                    <!-- 签名/附件 -->
                    <el-form-item label="签名" prop="sign" v-if="item.needSign">
                      <n-space vertical>
                        <n-image :width="120" v-if="item?.signUrl" :src="item?.signUrl" :preview-disabled="true" />
                        <el-tag
                          size="default"
                          type="danger"
                          style="cursor: pointer"
                          @click="
                            sign(url => {
                              item.signUrl = JPGlobal.getRealOCUrl(url)
                            })
                          "
                          >{{ !item?.signUrl ? '点击签名' : '重新签名' }}</el-tag
                        >
                      </n-space>
                    </el-form-item>

                    <el-form-item label="附件:" prop="attachment" v-if="item.needAttachment">
                      <!-- 上传附件 -->
                      <el-tag
                        size="default"
                        type="danger"
                        style="cursor: pointer"
                        @click="
                          attachment(options => {
                            item.attachmentList = options
                          })
                        "
                        >{{
                          item?.attachmentList && item?.attachmentList.length > 0 ? '重新上传附件' : '点击上传附件'
                        }}</el-tag
                      >

                      <!-- 附件 -->
                      <n-popselect
                        size="medium"
                        :on-update:value="changeAttachmentOption"
                        :options="item.attachmentList"
                        :render-label="renderAttachmentLabel"
                        v-if="item.attachmentList && item.attachmentList.length > 0"
                      >
                        <el-badge :value="item.attachmentList && item.attachmentList.length" class="item">
                          <el-tag type="success" size="default" style="margin-left: 10px">已上传附件</el-tag>
                        </el-badge>
                      </n-popselect>
                    </el-form-item>
                  </el-form>

                  <!-- 操作提示 -->
                  <el-alert
                    v-if="isOperationDisabled(item)"
                    type="warning"
                    show-icon
                    :closable="false"
                    style="margin-bottom: 15px"
                  >
                    <template #title>
                      <span>
                        {{ getOperationDisabledMessage(item) }}
                      </span>
                    </template>
                  </el-alert>

                  <div :class="isMobileDevice ? 'mobile-action-buttons' : 'desktop-action-buttons'">
                    <el-button type="success" @click="handleAudit(item, true)" :disabled="isOperationDisabled(item)">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        width="20"
                        height="20"
                        fill="currentColor"
                      >
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
                      </svg>
                      通过
                    </el-button>
                    <el-button type="danger" @click="handleAudit(item, false)" :disabled="isOperationDisabled(item)">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        width="20"
                        height="20"
                        fill="currentColor"
                      >
                        <path
                          d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                        />
                      </svg>
                      不通过
                    </el-button>
                    <el-button
                      type="primary"
                      @click="openTaskUpdateAssigneeForm(item.id)"
                      :disabled="isOperationDisabledDontNeedAttachment(item)"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        width="20"
                        height="20"
                        fill="currentColor"
                      >
                        <path d="M19 13h-4v4h-2v-4H9v-2h4V7h2v4h4v2z" />
                        <path d="M8 7h2v2H8zM8 11h2v2H8zM8 15h2v2H8z" />
                      </svg>
                      转办
                    </el-button>

                    <el-button
                      type="primary"
                      @click="handleDelegate(item)"
                      :disabled="isOperationDisabledDontNeedAttachment(item)"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        width="20"
                        height="20"
                        fill="currentColor"
                      >
                        <path
                          d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"
                        />
                      </svg>
                      委派
                    </el-button>
                    <el-button
                      type="primary"
                      @click="handleSign(item)"
                      :disabled="isOperationDisabledDontNeedAttachment(item)"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        width="20"
                        height="20"
                        fill="currentColor"
                      >
                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                      </svg>
                      加签
                    </el-button>
                    <el-button
                      type="warning"
                      @click="handleBack(item)"
                      :disabled="isOperationDisabledDontNeedAttachment(item)"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        width="20"
                        height="20"
                        fill="currentColor"
                      >
                        <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" />
                      </svg>
                      回退
                    </el-button>

                    <el-button type="default" @click="showFlowHelp = true"> 帮助 </el-button>
                  </div>
                </div>
              </el-card>
              <!-- 下一审批任务列表 -->
              <n-card
                v-if="nextTasks?.length"
                :class="['box-card', 'next-tasks-card', { 'mobile-next-tasks': isMobileDevice }]"
              >
                <template #header>
                  <div class="next-tasks-header">
                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
                      />
                    </svg>
                    <span>下一审批任务</span>
                  </div>
                </template>

                <!-- 移动端卡片式显示 -->
                <div v-if="isMobileDevice" class="mobile-next-tasks-list">
                  <div v-for="(task, index) in nextTasks" :key="index" class="mobile-task-item">
                    <div class="task-name">{{ task.name }}</div>
                    <div class="task-info">
                      <div class="assignee-info">
                        <span class="label">审批人：</span>
                        <span class="value">{{ task.assigneeUser?.empName || '未指定' }}</span>
                        <n-tag v-if="task.assigneeUser?.deptName" size="small" type="info" class="dept-tag">
                          {{ task.assigneeUser.deptName }}
                        </n-tag>
                      </div>
                      <div class="status-info">
                        <span class="label">状态：</span>
                        <el-tag :type="getTaskStatusType(task.status)" size="small">
                          {{ getTaskStatusLabel(task.status) }}
                        </el-tag>
                      </div>
                      <div v-if="task.createTime" class="time-info">
                        <span class="label">创建时间：</span>
                        <span class="value">{{ task.createTime }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 桌面端表格显示 -->
                <el-table v-else :data="nextTasks" border stripe>
                  <el-table-column prop="name" label="任务名称" min-width="120" />
                  <el-table-column label="审批人" min-width="100">
                    <template #default="{ row }">
                      <span>{{ row.assigneeUser?.empName || '未指定' }}</span>
                      <el-tag v-if="row.assigneeUser?.deptName" size="small" type="info" class="ml-2">
                        {{ row.assigneeUser.deptName }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="状态" min-width="80">
                    <template #default="{ row }">
                      <el-tag :type="getTaskStatusType(row.status)">
                        {{ getTaskStatusLabel(row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="createTime" label="创建时间" min-width="120" />
                </el-table>
              </n-card>
            </el-scrollbar>
          </n-tab-pane>

          <!-- Tab 3: 流程记录 -->
          <n-tab-pane name="history" tab="流程记录 流程图" display-directive="show"> </n-tab-pane>
        </n-tabs>

        <el-scrollbar :height="activeTab === 'history' ? (isMobileDevice ? '100%' : '85vh') : '0'">
          <!-- 高亮流程图 -->
          <div class="process-diagram-section" v-if="isMobileDevice">
            <ProcessInstanceBpmnViewer
              :id="`${id}`"
              :bpmn-xml="bpmnXml"
              :loading="processInstanceLoading"
              :process-instance="processInstance"
              :tasks="tasks"
              v-model:nextTasks="nextTasks"
              :class="{ 'mobile-diagram': isMobileDevice }"
            />
          </div>
          <!-- 审批记录 -->
          <ProcessInstanceTaskList
            :loading="tasksLoad"
            :process-instance="processInstance"
            :tasks="tasks"
            :runningTasks="runningTasks"
            @refresh="getTaskList"
            :class="{ 'mobile-task-list': isMobileDevice }"
          />

          <!-- 高亮流程图 -->
          <div class="process-diagram-section" v-if="!isMobileDevice">
            <ProcessInstanceBpmnViewer
              :id="`${id}`"
              :bpmn-xml="bpmnXml"
              :loading="processInstanceLoading"
              :process-instance="processInstance"
              :tasks="tasks"
              v-model:nextTasks="nextTasks"
              :class="{ 'mobile-diagram': isMobileDevice }"
            />
          </div>
        </el-scrollbar>
      </div>
    </n-spin>

    <!-- 弹窗：转办审批人 -->
    <TaskTransferForm ref="taskTransferFormRef" @success="getDetail" />
    <!-- 弹窗：回退节点 -->
    <TaskReturnForm ref="taskReturnFormRef" @success="getDetail" />
    <!-- 弹窗：委派，将任务委派给别人处理，处理完成后，会重新回到原审批人手中-->
    <TaskDelegateForm ref="taskDelegateForm" @success="getDetail" />
    <!-- 弹窗：加签，当前任务审批人为A，向前加签选了一个C，则需要C先审批，然后再是A审批，向后加签B，A审批完，需要B再审批完，才算完成这个任务节点 -->
    <TaskSignCreateForm ref="taskSignCreateFormRef" @success="getDetail" />
    <!-- 弹窗：签名 -->
    <j-modal title="签名" v-model:show="showSign" width="600px" height="200px" :show-btn="false">
      <template #content>
        <j-sign :canvas-width="620" @done="signDone" />
      </template>
    </j-modal>

    <!-- 弹窗：附件 -->
    <j-upload
      :show-button="false"
      v-model:show="showUpload"
      acceptProp=".pdf,.png,.jpg,.jpeg,.bmp,.gif,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar,.7z,.tar,.gz,.ofd"
      :maxAmount="10"
      @afterUpload="afterUpload"
    />
    <!-- 弹窗：附件预览 -->
    <j-preview
      v-model:show="showAttachmentPreview"
      :file="curAttachmentStepOption.value"
      :url="curAttachmentStepOption.value"
      :type="curAttachmentStepOption.type"
      style="z-index: 2100"
    />
    <!-- 显示id，右对齐，浅色样式 -->
    <div style="text-align: right; color: #bfbfbf; font-size: 13px; margin-top: 8px"> 流程ID：{{ id }} </div>
  </ContentWrap>

  <!-- 帮助弹窗 -->
  <n-modal title="帮助" v-model:show="showFlowHelp" height="100%" preset="card">
    <FlowHelp />
  </n-modal>
</template>
<script lang="ts" setup>
  import formCreate from '@form-create/element-ui' // import { useUserStore } from '@/store/modules/user'
  import { setConfAndFields2 } from '@/utils/bpmAdapter/formCreate'
  import type { ApiAttrs } from '@form-create/element-ui/types/config'
  import * as DefinitionApi from '@/api/bpm/definition'
  import * as ProcessInstanceApi from '@/api/bpm/processInstance'
  import * as TaskApi from '@/api/bpm/task'
  import * as ActivityApi from '@/api/bpm/activity'
  import ProcessInstanceBpmnViewer from './ProcessInstanceBpmnViewer.vue'
  import ProcessInstanceTaskList from './ProcessInstanceTaskList.vue'
  import TaskReturnForm from './dialog/TaskReturnForm.vue'
  import TaskDelegateForm from './dialog/TaskDelegateForm.vue'
  import TaskTransferForm from './dialog/TaskTransferForm.vue'
  import TaskSignCreateForm from './dialog/TaskSignCreateForm.vue'

  import { registerComponent } from '@/router'
  import * as UserApi from '@/api/bpm/bpmAdapter/user'
  import { Option } from '@jtypes'

  import {
    getCurrentInstance,
    h,
    nextTick,
    onMounted,
    reactive,
    ref,
    shallowRef,
    toRaw,
    unref,
    watch,
    computed,
  } from 'vue'
  import { useMessage } from '@/components/common/bpm/bpmAdapter/useMessage'
  import { useRoute } from 'vue-router'
  import { useUserStore } from '@/store'
  import { NTabs, NTag } from 'naive-ui'
  import { isMobileDevice as checkMobileDevice } from '@/utils/device'
  import { ElTag } from 'element-plus'
  import JPGlobal from '@/types/common/jglobal'
  // import { computed } from 'vue'
  // import BusinessFormComponent from './BusinessFormComponent.vue'
  // import { useDict } from '@/components/common/bpm/bpmAdapter/useDict'
  // import { formatDate } from '@/utils/bpmAdapter/formatTime'
  import { getIntDictOptions, DICT_TYPE } from '@/utils/bpmAdapter/bpmDictAdapter'
  // import { ContentWrap } from '@/components/ContentWrap'

  import FlowHelp from './components/flowHelp.vue'
  const showFlowHelp = ref(false)

  // 移动端相关
  const isMobileDevice = computed(() => checkMobileDevice())
  const showDiagramModal = ref(false)
  const activityList = ref([]) // 活动列表，用于流程图

  // 定义接口类型
  interface ProcessInstance {
    id: string
    name: string
    startUser: {
      empName: string
      deptName: string
      empCode: string
      deptCode: string
    }
    processDefinition: {
      id: number
      formType: number
      formConf: any
      formFields: any
      formCustomViewPath: string
    }
    formVariables: Record<string, any>
  }

  interface Task {
    id: string
    name: string
    status: number
    assigneeUser?: {
      empCode: string
    }
    formId?: number
    formConf?: any
    formFields?: any
    formVariables?: Record<string, any>
    needSign?: boolean
    signUrl?: string
    needAttachment?: boolean
    attachmentList?: Array<{
      label: string
      file: File
      fileUrl: string
      type: string
    }>
    children?: Task[]
    endTime?: number
    createTime?: number
    propertyMap?: {
      cashAudit?: string
      [key: string]: any
    }
  }

  interface AuditForm {
    reason: string
    copyUserIds: string[]
    auditTime?: string
  }

  interface ApproveForm {
    value: Record<string, any>
    option: Record<string, any>
    rule: any[]
  }

  onMounted(() => {
    setTimeout(() => {
      console.log(fApi)
    }, 10000)
    if (isMobileDevice.value) {
      for (let i = 0; i < 2; i++) {}
      nextTick(() => {
        setTimeout(() => {
          activeTab.value = 'application'
          ntabsRef.value?.handleSegmentResize()
          setTimeout(() => {
            ntabsRef.value?.handleSegmentResize()
          }, 300)
        }, 300)
      })
    }
  })

  defineOptions({ name: 'BpmProcessInstanceDetail' })

  const props = defineProps({
    id: {
      type: String,
      default: '',
    },
    otherProps: {
      type: Object,
      default: () => ({}),
    },
  })

  const emits = defineEmits(['close'])

  const ntabsRef = ref<InstanceType<typeof NTabs>>()
  onMounted(() => {
    console.log(ntabsRef.value)
  })

  const activeTab = ref('application')
  const activeTabChange = (tab: string) => {
    let renderNameList = ntabsRef.value?.renderNameListRef.value
    if (Array.isArray(ntabsRef.value?.renderNameListRef.value)) {
      nextTick(() => {
        if (renderNameList.includes(tab)) {
          activeTab.value = tab || 'application'
        }
      })
    } else {
      activeTab.value = tab || 'application'
    }
  }

  const { query } = useRoute() // 路由查询参数
  const message = useMessage() // 消息提示工具
  const { proxy } = getCurrentInstance() as any

  // 当前用户信息
  const userId = useUserStore().getUserInfo?.hrmUser?.empCode // 当前登录用户编号
  const id = props.id || (query.id as unknown as string) // 流程实例编号

  // 加载状态
  const processInstanceLoading = ref(false) // 流程实例加载状态
  const tasksLoad = ref(true) // 任务列表加载状态
  const runningTaskLoading = ref(true)

  // 流程相关数据
  const processInstance = ref<ProcessInstance>({} as ProcessInstance) // 流程实例数据
  const bpmnXml = ref('') // BPMN XML 数据
  const tasks = ref<Task[]>([]) // 任务列表

  // 审批相关数据
  const runningTasks = ref<Task[]>([]) // 运行中的任务列表
  const auditForms = ref<AuditForm[]>([]) // 审批表单数据
  const auditRule = reactive({
    reason: [{ required: true, message: '审批建议不能为空', trigger: 'blur' }],
  })
  const approveForms = ref<ApproveForm[]>([]) // 审批通过时的补充信息
  const approveFormFApis = ref<ApiAttrs[]>([]) // approveForms 的表单 API

  // 表单相关
  const fApi = ref<ApiAttrs>() // 表单 API
  const detailForm = ref<{
    rule: any[]
    option: Record<string, any>
    value: Record<string, any>
  }>({
    rule: [],
    option: {},
    value: {},
  })

  // 业务表单组件
  const businessFormComponentRef = ref()
  const BusinessFormComponent = shallowRef(null) // 异步加载的业务表单组件

  // 审批前处理函数
  const handleAuditBefoerFn = ref<Function>(() => {})
  const setHandleAuditBefoerFn = (fn: Function) => {
    handleAuditBefoerFn.value = fn
  }

  // 用户选项
  const userOptions = ref<UserApi.UserVO[]>([]) // 用户列表选项

  // 审批建议选项
  const reasonOptions = ref<
    Array<{ label: string; value: string; type: 'default' | 'error' | 'primary' | 'info' | 'success' | 'warning' }>
  >([
    { label: '同意', value: '同意', type: 'success' },
    { label: '不同意', value: '不同意', type: 'error' },
    { label: '缺少信息请补充', value: '缺少信息请补充', type: 'warning' },
    // { label: '转办', value: '转办', type: 'info' },
    // { label: '委派', value: '委派', type: 'primary' },
    // { label: '加签', value: '加签', type: 'success' },
    // { label: '退回', value: '退回', type: 'error' },
  ])

  // 签名和附件相关
  let curSignUrlCallBack: (url: string) => void = () => {}
  let curAttachmentCallBack: (
    options: Array<{
      file: File
      label: string
      value: string
      type: 'pdf' | 'image'
    }>
  ) => void = () => {}
  const showSign = ref(false)
  const showUpload = ref(false)
  const showAttachmentPreview = ref(false)

  /** 监听 approveFormFApis，实现它对应的 form-create 初始化后，隐藏掉对应的表单提交按钮 */
  watch(
    () => approveFormFApis.value,
    value => {
      value?.forEach(api => {
        api.btn.show(false)
        api.resetBtn.show(false)
      })
    },
    {
      deep: true,
    }
  )

  //关闭流程详情
  const close = () => {
    emits('close')
  }

  /**
   * 检查操作是否应该被禁用
   * @param task 当前任务
   * @returns 如果操作应该被禁用，则返回true
   */
  const isOperationDisabled = (task: Task): boolean => {
    if (task.needSign && !task.signUrl) {
      return true
    }
    if (task.needAttachment && (!task.attachmentList || task.attachmentList.length === 0)) {
      return true
    }
    return false
  }

  const isOperationDisabledDontNeedAttachment = (task: Task): boolean => {
    if (task.needSign && !task.signUrl) {
      return true
    }
    return false
  }

  /**
   * 获取操作被禁用的提示信息
   * @param task 当前任务
   * @returns 提示信息
   */
  const getOperationDisabledMessage = (task: Task): string => {
    const messages = []
    if (task.needSign && !task.signUrl) {
      messages.push('请先完成签名')
    }
    // if (task.needAttachment && (!task.attachmentList || task.attachmentList.length === 0)) {
    //   messages.push('请先上传附件')
    // }
    return messages.join('，')
  }

  /** 处理审批通过和不通过的操作 */
  const handleAudit = async (task: any, pass: boolean) => {
    // 检查是否需要签名或附件
    if (isOperationDisabled(task)) {
      message.warning(getOperationDisabledMessage(task))
      return
    }

    // 1. 执行论文认定的 beforeFlowAuditEvents（自定义组件） 方法
    let formVariables = {} // 定义一个变量来存储流程变量
    try {
      if (businessFormComponentRef.value?.beforeFlowAuditEvents) {
        const result = await businessFormComponentRef.value.beforeFlowAuditEvents(task, pass)
        if (!result) {
          // 如果子组件返回false,则中断后续操作
          return false
        }
        // 如果子组件返回的是对象且包含variables，则保存变量
        if (result && typeof result === 'object' && result.variables) {
          formVariables = result.variables // 保存变量，稍后在submitAudit中使用
        }
      }
    } catch (error) {
      console.error('beforeFlowAuditEvents error:', error)
      return false
    }

    // 1.1 获得对应表单
    const index = runningTasks.value.indexOf(task)
    const auditFormRef = proxy.$refs['form' + index][0]
    // 1.2 校验表单
    const elForm = unref(auditFormRef)
    if (!elForm) return
    const valid = await elForm.validate()
    if (!valid) return
    // 1.2.1 提交审批前
    if (handleAuditBefoerFn.value) {
      let flag = await handleAuditBefoerFn.value(pass)
      // console.log('@@-flag', flag)
      if (flag != undefined && !flag) return
    }

    // 显示确认对话框
    window.$dialog[pass ? 'success' : 'error']({
      title: '审批确认',
      content: `确定要${pass ? '通过' : '不通过'}该审批吗？`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        submitAudit(task, pass, index, formVariables)
      },
    })
  }

  /** 提交审批 */
  const submitAudit = async (task: any, pass: boolean, index: number, formVariables: any = {}) => {
    try {
      // 2.1 提交审批
      const data = {
        id: task.id,
        reason: auditForms.value[index].reason,
        auditTime: auditForms.value[index].auditTime,
        copyUserIds: auditForms.value[index].copyUserIds,
        variables: { ...formVariables }, // 使用传入的变量
        attachmentList: [],
      }

      // 为论文有效性认定流程添加自定义变量
      // if (processInstance.value?.processDefinition?.key === 'PAPER_EFFECTIVENESS_CONDITION') {
      //   const auditForm = auditForms.value[index]
      //   if (auditForm.paperCategory) data.variables['PAPER_CATEGORY'] = auditForm.paperCategory
      //   if (auditForm.impactFactor) data.variables['IMPACT_FACTOR'] = auditForm.impactFactor
      //   if (auditForm.validationResult) data.variables['VALIDATION_RESULT'] = auditForm.validationResult
      //   if (auditForm.rewardLevel) data.variables['REWARD_LEVEL'] = auditForm.rewardLevel
      // }

      if (pass) {
        // 审批通过，并且有额外的 approveForm 表单，需要校验 + 拼接到 data 表单里提交
        const formCreateApi = approveFormFApis.value[index]
        if (formCreateApi) {
          await formCreateApi.validate()

          Object.assign(data.variables, approveForms.value[index].value)
        }
        // 签名
        if (task.needSign) {
          if (task.signUrl) {
            data.variables['TASK_SIGN_URL'] = task.signUrl
          } else {
            message.error('需要签名')
            return
          }
        }

        if (task.needAttachment) {
          if (task.attachmentList && task.attachmentList.length > 0) {
            data.attachmentList = toRaw(task.attachmentList)
          } else {
            message.error('需要上传附件')
            return
          }
        }
        let formData = new FormData()
        for (const key in data) {
          if (key === 'attachmentList') {
            data[key]?.forEach((attachment, index) => {
              formData.append(`attachmentList[${index}].label`, attachment.label)
              formData.append(`attachmentList[${index}].file`, attachment.file)
              formData.append(`attachmentList[${index}].fileUrl`, attachment.fileUrl)
              formData.append(`attachmentList[${index}].type`, attachment.type)
            })
          } else if (key === 'copyUserIds') {
            data[key]?.forEach(userId => {
              // formData.append(`copyUserIds[${index}]`, userId)
              formData.append(`copyUserIds`, userId)
            })
          } else if (key === 'variables') {
            for (const varKey in data[key]) {
              formData.append(`variables[${varKey}]`, data[key][varKey])
            }
          } else {
            if (data[key]) formData.append(key, data[key])
          }
        }

        tasksLoad.value = true
        try {
          await TaskApi.approveTask(formData)
          message.success('审批通过成功')
          //切换到流程图

          activeTabChange('history')
        } finally {
          tasksLoad.value = false
        }
      } else {
        tasksLoad.value = true
        try {
          await TaskApi.rejectTask(data)
          message.success('审批不通过成功')
          //切换到流程图
          activeTabChange('history')
        } finally {
          tasksLoad.value = false
        }
      }
      // 2.2 加载最新数据
      getDetail()
      //防止网络延时
      setTimeout(getDetail, 2000)
    } catch (error) {
      console.error('submitAudit error:', error)
    }
  }

  /** 转办审批人 */
  const taskTransferFormRef = ref()
  const openTaskUpdateAssigneeForm = async (taskId: string) => {
    taskTransferFormRef.value.open(taskId)
  }

  /** 处理审批退回的操作 */
  const taskDelegateForm = ref()
  const handleDelegate = async (task: any) => {
    tasksLoad.value = true
    try {
      await taskDelegateForm.value.open(task.id)
    } finally {
      tasksLoad.value = false
    }
  }

  /** 处理审批退回的操作 */
  const taskReturnFormRef = ref()
  const handleBack = async (task: any) => {
    tasksLoad.value = true
    try {
      await taskReturnFormRef.value.open(task.id)
    } finally {
      tasksLoad.value = false
    }
  }

  /** 处理审批加签的操作 */
  const taskSignCreateFormRef = ref()
  const handleSign = async (task: any) => {
    tasksLoad.value = true
    try {
      await taskSignCreateFormRef.value.open(task.id)
    } finally {
      tasksLoad.value = false
    }
  }

  /** 获得详情 */
  const getDetail = () => {
    // 1. 获得流程实例相关
    getProcessInstance(() => getTaskList())
    // 2. 获得流程任务列表（审批记录）
  }

  /** 加载流程实例 */
  const getProcessInstance = async (getTaskList: any) => {
    try {
      processInstanceLoading.value = true
      tasksLoad.value = true
      const data = await ProcessInstanceApi.getProcessInstance(id)
      if (!data) {
        message.error('查询不到流程信息！')
        return
      }
      processInstance.value = data

      // 设置表单信息
      const processDefinition = data.processDefinition
      if (processDefinition.formType === 10) {
        setConfAndFields2(
          detailForm,
          processDefinition.formConf,
          processDefinition.formFields,
          data.formVariables,
          false
        )
        nextTick().then(() => {
          fApi.value?.btn.show(false)
          fApi.value?.resetBtn.show(false)
          //@ts-ignore
          fApi.value?.disabled(true)
        })
      } else {
        let component = data.processDefinition.formCustomViewPath
        BusinessFormComponent.value = registerComponent(component)
      }
      // 加载流程图
      tasksLoad.value = true
      try {
        bpmnXml.value = (await DefinitionApi.getProcessDefinition(processDefinition.id as number))?.bpmnXml
        // 加载活动数据
        if (id) {
          activityList.value = await ActivityApi.getActivityList({
            processInstanceId: id,
          })
        }
      } finally {
        tasksLoad.value = false
      }
    } finally {
      getTaskList()
      processInstanceLoading.value = false
      tasksLoad.value = false
    }
  }

  /** 加载任务列表 */
  const getTaskList = async () => {
    runningTasks.value = []
    auditForms.value = []
    approveForms.value = []
    approveFormFApis.value = []
    runningTaskLoading.value = true
    try {
      // 获得未取消的任务
      tasksLoad.value = true
      const data = await TaskApi.getTaskListByProcessInstanceId(id)

      tasks.value = []
      // 1.1 移除已取消的审批
      data.forEach((task: any) => {
        if (task.status !== 4) {
          tasks.value.push(task)
        }
      })
      // 1.2 排序，将未完成的排在前面，已完成的排在后面；
      tasks.value.sort((a, b) => {
        // 有已完成的情况，按照完成时间倒序
        if (a.endTime && b.endTime) {
          return b.endTime - a.endTime
        } else if (a.endTime) {
          return 1
        } else if (b.endTime) {
          return -1
          // 都是未完成，按照创建时间倒序
        } else {
          return b.createTime - a.createTime
        }
      })

      // 获得需要自己审批的任务
      loadRunningTask(tasks.value)
    } finally {
      tasksLoad.value = false
      runningTaskLoading.value = false
    }
  }

  const loadRunningTask = (tasks: any[], parentTask: any = null, isChild = false) => {
    tasks.forEach((task: any) => {
      if (task.children) {
        loadRunningTask(task.children, task, true)
      }
      // 2.1 只有待处理才需要
      if (task.status !== 1 && task.status !== 6) {
        return
      }
      // 2.2 自己不是处理人 2024年08月14日14:28:50
      if (!task.assigneeUser || task.assigneeUser.empCode !== userId) {
        return
      }
      // 2.3 添加到处理任务
      if (isChild) {
        // 子审批需要复制父审批的流程表单（情况 转办，前后加签） todo 判断

        console.log('@@-parentTask', parentTask)
        console.log('@@-task', task)
        task = {
          ...task,
          formId: parentTask.formId,
          formName: parentTask.formName,
          formConf: parentTask.formConf,
          formFields: parentTask.formFields,
          formVariables: parentTask.formVariables,
        }

        runningTasks.value.push({
          ...task,
          formId: parentTask.formId,
          formName: parentTask.formName,
          formConf: parentTask.formConf,
          formFields: parentTask.formFields,
          formVariables: parentTask.formVariables,
        })
      } else {
        runningTasks.value.push({ ...task })
      }

      auditForms.value.push({
        reason: '',
        copyUserIds: [],
      })
      // ============
      //判断是否是出纳环节,出纳不填写意见
      if (runningTasks.value[0]?.propertyMap?.cashAudit == '1') {
        auditForms.value[0].reason = ' '
      }
      // ============

      // 2.4 处理 approve 表单
      if (task.formId && task.formConf) {
        const approveForm = {
          value: {},
          option: {},
          rule: [],
        }
        setConfAndFields2(
          approveForm,
          task.formConf,
          task.formFields,
          Object.assign(processInstance.value.formVariables, task.formVariables),
          false
        )
        approveForms.value.push(approveForm)
      } else {
        approveForms.value.push({
          value: {},
          option: {},
          rule: [],
        }) // 占位，避免为空
      }
    })
  }

  /** 初始化 */
  onMounted(async () => {
    getDetail()
    // 获得用户列表
    tasksLoad.value = true
    try {
      userOptions.value = await UserApi.getSimpleUserList()
    } finally {
      tasksLoad.value = false
    }
  })

  /**
   * 签名完成回调
   * @param signPath 签名图片路径
   */
  const signDone = (signPath: string): void => {
    curSignUrlCallBack(signPath)
    showSign.value = false
  }

  /**
   * 打开附件上传对话框
   * @param callBack 上传完成后的回调函数
   */
  const attachment = (
    callBack: (
      options: Array<{
        file: File
        label: string
        value: string
        type: 'pdf' | 'image'
      }>
    ) => void
  ): void => {
    curAttachmentCallBack = callBack
    showUpload.value = true
  }

  /**
   * 文件上传完成后的处理
   * @param files 上传的文件列表
   */
  const afterUpload = (files: File[]): void => {
    const options: Array<{
      file: File
      label: string
      value: string
      type: 'pdf' | 'image'
    }> = files.map(file => ({
      file,
      label: file.name,
      value: file.name.toLowerCase().includes('.pdf') ? (file as any) : URL.createObjectURL(file),
      type: file.name.toLowerCase().includes('.pdf') ? 'pdf' : 'image',
    }))
    curAttachmentCallBack(options)
  }

  /**
   * 附件选项变更处理
   * @param val 选中的值
   * @param option 选中的选项
   */
  const changeAttachmentOption = (_val: string, option: Option): void => {
    showAttachmentPreview.value = true
    curAttachmentStepOption.value = option
  }

  /**
   * 渲染附件标签
   * @param option 附件选项
   * @returns 渲染的标签元素
   */
  const renderAttachmentLabel = (option: Option): any => {
    return h(ElTag, { size: 'small', style: { cursor: 'pointer' } }, () => option.label)
  }

  /**
   * 打开签名对话框
   * @param callBack 签名完成后的回调函数
   */
  const sign = (callBack: (url: string) => void): void => {
    curSignUrlCallBack = callBack
    showSign.value = true
  }

  // 附件预览相关
  const curAttachmentStepOption = ref<any>({
    value: '',
    type: 'image',
    label: '',
  })

  // 添加 nextTasks 数据变量
  const nextTasks = ref<Task[]>([])

  // 获取审批任务状态映射
  const taskStatusOptions = getIntDictOptions(DICT_TYPE.BPM_TASK_STATUS)

  // 根据状态值获取对应的标签类型
  const getTaskStatusType = (status: number) => {
    switch (status) {
      case 0:
        return 'info' // 待处理
      case 1:
        return 'primary' // 处理中
      case 2:
        return 'success' // 已完成
      case 3:
        return 'danger' // 已拒绝
      case 4:
        return 'warning' // 已取消
      case 5:
        return 'warning' // 已回退
      case 6:
        return 'primary' // 已委派
      case 7:
        return 'success' // 已转办
      default:
        return 'info'
    }
  }

  // 根据状态值获取对应的标签文本
  const getTaskStatusLabel = (status: number) => {
    const option = taskStatusOptions.find(item => item.value === status)
    return option ? option.label : '未知状态'
  }

  /**
   * 选择快捷审批建议
   * @param index 表单索引
   * @param value 建议内容
   */
  const selectQuickReason = (index: number, value: string): void => {
    auditForms.value[index].reason = value
  }

  /**
   * 处理下一任务更新
   * @param tasks 下一任务列表
   */
  const handleNextTasks = (tasks: Task[]): void => {
    nextTasks.value = tasks
  }
</script>

<style></style>

<style scoped>
  /* 使用更高优先级的选择器 */
  body :not(#fake-id) * {
    color: red !important;
  }
  .tabs-container {
    height: 100%;
  }

  /* 移动端布局优化 */
  .mobile-layout {
    padding: 0 6px;
  }

  .mobile-layout :deep(.el-tabs-nav) {
    padding: 0 0px;
  }

  .mobile-layout :deep(.el-tabs-tab) {
    padding: 12px 16px;
    font-size: 16px;
  }

  .mobile-layout :deep(.el-tabs-pane) {
    padding: 16px;
  }

  /* 流程图相关样式 */
  .process-diagram-section {
    position: relative;
  }

  .mobile-diagram {
    min-height: 300px;
  }

  .mobile-diagram-actions {
    margin-top: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
  }

  /* 移动端审批卡片优化 */
  .mobile-approval-card {
    margin: 12px 0;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .mobile-card-header {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
  }

  .mobile-form-container {
    padding: 0;
  }

  .desktop-form-container {
    padding: 0 5% 0 5%;
  }

  .mobile-form {
    padding: 0;
  }

  .mobile-form :deep(.el-form-item) {
    margin-bottom: 20px;
  }

  .mobile-form :deep(.el-form-item__label) {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    line-height: 1.4;
  }

  /* 移动端操作按钮 */
  .mobile-action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin: 20px 0;
    padding: 0;
  }

  .desktop-action-buttons {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    margin-left: 0%;
    font-size: 14px;
    flex-wrap: wrap;
    gap: 8px;
  }

  /* 移动端下一任务列表 */
  .mobile-next-tasks {
    margin: 16px 0;
    border-radius: 12px;
  }

  .mobile-next-tasks-list {
    padding: 0;
  }

  .mobile-task-item {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
    border-radius: 8px;
    margin-bottom: 12px;
  }

  .mobile-task-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }

  .mobile-task-item .task-name {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 12px;
  }

  .mobile-task-item .task-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .mobile-task-item .label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
  }

  .mobile-task-item .value {
    font-size: 14px;
    color: #333;
  }

  .mobile-task-item .dept-tag {
    margin-left: 8px;
  }

  /* 移动端流程图放大按钮 */
  .mobile-diagram-actions-top {
    margin: 16px;
    padding: 16px;

    border-radius: 12px;
  }

  .mobile-diagram-actions-top .n-button {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-weight: 600;
    backdrop-filter: blur(10px);
  }

  .mobile-diagram-actions-top .n-button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    transition: all 0.3s ease;
  }

  /* 移动端任务列表 */
  .mobile-task-list {
    margin: 16px 0;
  }

  /* 移动端表单优化 */
  @media (max-width: 768px) {
    .tabs-container {
      padding: 0;
    }

    :deep(.el-card) {
      margin: 8px 0;
      border-radius: 12px;
      border: 1px solid #e8e8e8;
    }

    :deep(.el-card__header) {
      padding: 16px 20px;
      font-size: 16px;
      font-weight: 600;
      background: #f8f9fa;
      border-bottom: 1px solid #e8e8e8;
    }

    :deep(.el-form-item) {
      margin-bottom: 20px;
    }

    :deep(.el-form-item__label) {
      font-size: 15px;
      font-weight: 500;
      margin-bottom: 8px;
      display: block;
      text-align: left;
      color: #333;
    }

    :deep(.el-input__inner) {
      font-size: 16px;
      padding: 14px 16px;
      min-height: 48px;
      border-radius: 8px;
      border: 1px solid #d9d9d9;
    }

    :deep(.el-textarea__inner) {
      font-size: 16px;
      padding: 14px 16px;
      min-height: 100px;
      border-radius: 8px;
      border: 1px solid #d9d9d9;
    }

    :deep(.el-button) {
      min-height: 48px;
      font-size: 15px;
      padding: 12px 20px;
      margin: 4px;
      border-radius: 8px;
      font-weight: 500;
    }

    :deep(.n-button) {
      min-height: 48px;
      font-size: 15px;
      border-radius: 8px;
      font-weight: 500;
    }

    :deep(.n-tag) {
      font-size: 13px;
      padding: 6px 12px;
      border-radius: 6px;
    }

    :deep(.n-date-picker) {
      width: 100%;
    }

    :deep(.n-date-picker .n-input) {
      min-height: 48px;
    }

    /* 快捷标签优化 */
    .quick-reason-tags {
      margin-top: 12px;
    }

    .quick-reason-tags :deep(.n-tag) {
      margin: 4px 8px 4px 0;
      padding: 8px 12px;
      font-size: 14px;
    }
  }

  /* 下一审批任务样式 */
  .next-tasks-card {
    margin-top: 16px;
    margin-bottom: 16px;
  }

  .next-tasks-header {
    display: flex;
    align-items: center;
    gap: 6px;

    svg {
      color: #409eff;
    }
  }

  .next-tasks-list {
    padding: 8px 0;
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  /* 快捷审批建议标签样式 */
  .quick-reason-tags {
    margin-top: 5px;
  }
  .el-popover-self {
    min-width: 80px !important;
    width: 80px !important;
    padding: 0;
  }
</style>
