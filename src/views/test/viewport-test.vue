<!--
  移动端视口管理测试页面
  用于验证底部导航栏遮挡问题的解决效果
-->
<template>
  <div class="viewport-test-page">
    <!-- 信息面板 -->
    <div class="info-panel">
      <h2>📱 视口信息测试</h2>
      
      <div class="info-grid">
        <div class="info-item">
          <label>设备类型:</label>
          <span :class="{ mobile: isMobile, desktop: !isMobile }">
            {{ isMobile ? '移动端' : '桌面端' }}
          </span>
        </div>
        
        <div class="info-item">
          <label>可视高度:</label>
          <span>{{ viewportInfo?.visualHeight || 'N/A' }}px</span>
        </div>
        
        <div class="info-item">
          <label>窗口高度:</label>
          <span>{{ viewportInfo?.windowHeight || 'N/A' }}px</span>
        </div>
        
        <div class="info-item">
          <label>动态VH:</label>
          <span>{{ dynamicVH }}</span>
        </div>
      </div>
      
      <!-- 安全区域信息 -->
      <div class="safe-area-info">
        <h3>🛡️ 安全区域</h3>
        <div class="safe-area-grid">
          <div>顶部: {{ safeArea.top }}px</div>
          <div>底部: {{ safeArea.bottom }}px</div>
          <div>左侧: {{ safeArea.left }}px</div>
          <div>右侧: {{ safeArea.right }}px</div>
        </div>
      </div>
      
      <!-- CSS变量信息 -->
      <div class="css-vars-info">
        <h3>🎨 CSS变量</h3>
        <div class="css-vars-list">
          <div v-for="(value, key) in cssVars" :key="key" class="css-var-item">
            <code>{{ key }}</code>: <span>{{ value }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 测试内容区域 -->
    <div class="test-content">
      <h3>📋 滚动测试内容</h3>
      <p>这是一个用于测试移动端视口管理的页面。请在移动端设备上打开此页面，测试以下功能：</p>
      
      <div class="test-sections">
        <section class="test-section">
          <h4>🔍 视口检测测试</h4>
          <ul>
            <li>旋转设备，观察视口信息变化</li>
            <li>滚动页面，观察浏览器工具栏隐藏/显示</li>
            <li>检查底部内容是否被导航栏遮挡</li>
          </ul>
        </section>
        
        <section class="test-section">
          <h4>📏 高度计算测试</h4>
          <div class="height-test-box" :style="{ height: safeContentHeight }">
            <p>这个盒子的高度使用安全内容高度计算</p>
            <p>高度值: {{ safeContentHeight }}</p>
          </div>
        </section>
        
        <section class="test-section">
          <h4>🎯 安全区域测试</h4>
          <div class="safe-area-test">
            <div class="corner top-left">左上角</div>
            <div class="corner top-right">右上角</div>
            <div class="corner bottom-left">左下角</div>
            <div class="corner bottom-right">右下角</div>
          </div>
        </section>
        
        <!-- 填充内容，用于测试滚动 -->
        <section class="test-section" v-for="i in 10" :key="i">
          <h4>📄 测试内容 {{ i }}</h4>
          <p>这是第 {{ i }} 段测试内容。用于测试页面滚动时的视口变化。</p>
          <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        </section>
      </div>
    </div>
    
    <!-- 底部测试区域 -->
    <div class="bottom-test-area">
      <h3>⬇️ 底部测试区域</h3>
      <p>如果这个区域被底部导航栏遮挡，说明视口管理需要调整。</p>
      <button @click="refresh" class="refresh-btn">🔄 刷新视口信息</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useMobileLayout } from '@/composables/useViewport'

// 使用移动端布局管理
const {
  viewportInfo,
  isMobile,
  dynamicVH,
  safeContentHeight,
  safeArea,
  cssVars,
  refresh
} = useMobileLayout()

// 计算属性用于显示
const displayInfo = computed(() => {
  if (!viewportInfo.value) return null
  
  return {
    ...viewportInfo.value,
    heightDiff: viewportInfo.value.windowHeight - viewportInfo.value.visualHeight
  }
})
</script>

<style scoped>
.viewport-test-page {
  padding: 16px;
  max-width: 100%;
  overflow-x: hidden;
}

.info-panel {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-panel h2 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 18px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.info-item span {
  font-size: 14px;
  font-weight: 600;
}

.info-item span.mobile {
  color: #10b981;
}

.info-item span.desktop {
  color: #3b82f6;
}

.safe-area-info,
.css-vars-info {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.safe-area-info h3,
.css-vars-info h3 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #374151;
}

.safe-area-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  font-size: 12px;
}

.css-vars-list {
  max-height: 200px;
  overflow-y: auto;
  font-size: 11px;
}

.css-var-item {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
  border-bottom: 1px solid #f3f4f6;
}

.css-var-item code {
  background: #f3f4f6;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 10px;
}

.test-content {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-sections {
  margin-top: 16px;
}

.test-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.test-section:last-child {
  border-bottom: none;
}

.test-section h4 {
  margin: 0 0 12px 0;
  color: #374151;
  font-size: 16px;
}

.test-section ul {
  margin: 8px 0;
  padding-left: 20px;
}

.test-section li {
  margin-bottom: 4px;
  font-size: 14px;
  color: #6b7280;
}

.height-test-box {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  padding: 16px;
  color: white;
  text-align: center;
  margin: 12px 0;
}

.safe-area-test {
  position: relative;
  height: 200px;
  background: #f3f4f6;
  border-radius: 8px;
  margin: 12px 0;
}

.corner {
  position: absolute;
  background: #ef4444;
  color: white;
  padding: 8px;
  font-size: 12px;
  border-radius: 4px;
}

.corner.top-left {
  top: var(--safe-area-top, 8px);
  left: var(--safe-area-left, 8px);
}

.corner.top-right {
  top: var(--safe-area-top, 8px);
  right: var(--safe-area-right, 8px);
}

.corner.bottom-left {
  bottom: var(--safe-area-bottom, 8px);
  left: var(--safe-area-left, 8px);
}

.corner.bottom-right {
  bottom: var(--safe-area-bottom, 8px);
  right: var(--safe-area-right, 8px);
}

.bottom-test-area {
  background: #fef3c7;
  border: 2px solid #f59e0b;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  margin-bottom: 32px; /* 额外的底部边距 */
}

.bottom-test-area h3 {
  margin: 0 0 8px 0;
  color: #92400e;
}

.bottom-test-area p {
  margin: 8px 0;
  color: #92400e;
  font-size: 14px;
}

.refresh-btn {
  background: #10b981;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.refresh-btn:hover {
  background: #059669;
}

.refresh-btn:active {
  transform: scale(0.98);
}

/* 移动端优化 */
@media (max-width: 768px) {
  .viewport-test-page {
    padding: 12px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .safe-area-grid {
    grid-template-columns: 1fr;
  }
  
  .css-vars-list {
    max-height: 150px;
  }
  
  .css-var-item {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
