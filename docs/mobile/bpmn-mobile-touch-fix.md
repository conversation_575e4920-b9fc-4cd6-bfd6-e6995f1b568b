# BPMN移动端触摸点击显示Hover信息修复

## 问题描述

在移动端使用BPMN流程图查看器时，用户无法通过触摸点击来显示节点的详细信息(hover信息)，因为移动设备没有鼠标hover概念。

## 解决方案

### 1. 修改 `useOverlayManager.ts`

- 添加了移动端点击显示overlay的支持
- 新增 `toggleMobileElementOverlay` 方法处理移动端点击切换
- 添加自动隐藏功能（15秒后自动隐藏）
- 支持点击同一元素切换显示/隐藏
- 添加定时器管理避免内存泄漏

**主要变更：**
```typescript
// 新增移动端点击处理方法
const toggleMobileElementOverlay = (element, ..., autoHide = true) => {
  // 点击同一元素时切换显示/隐藏
  if (currentMobileOverlayElementId === element.id) {
    removeElementOverlay(element)
    return
  }
  
  // 显示新overlay并设置自动隐藏
  showElementOverlay(element, ..., true) // 传递isMobile=true
  // 15秒后自动隐藏
  setTimeout(() => removeElementOverlay(element), 15000)
}
```

### 2. 修改 `useBpmnViewer.ts`

- 区分桌面端和移动端事件监听
- 桌面端：保持原有的 `element.hover` 和 `element.out` 事件
- 移动端：使用 `element.click` 事件代替hover
- 添加画布点击事件，点击空白区域隐藏overlay

**主要变更：**
```typescript
// 桌面端hover事件
if (!isMobileDevice.value) {
  eventBus.on('element.hover', ...)
  eventBus.on('element.out', ...)
}

// 移动端点击事件
if (isMobileDevice.value) {
  eventBus.on('element.click', (eventObj) => {
    // 阻止事件冒泡避免触发拖拽
    eventObj.originalEvent?.stopPropagation()
    overlayManager.toggleMobileElementOverlay(...)
  })
  
  // 点击空白区域隐藏overlay
  eventBus.on('canvas.click', ...)
}
```

### 3. 优化 `useInteractionController.ts`

解决了移动端点击和拖拽冲突的问题：

- **延迟判断**: 触摸开始后延迟150ms判断是否为拖拽
- **移动检测**: 只有移动超过5像素才认为是拖拽
- **点击优先**: 短时间无移动的触摸优先作为点击处理

```typescript
// 添加点击/拖拽区分逻辑
let touchStartTime = 0
let touchMoved = false
let dragDelayTimer: any = null
const DRAG_DELAY = 150 // 150ms延迟来区分点击和拖拽
```

### 4. 添加移动端样式支持

在 `index.scss` 中添加了移动端特有的overlay样式：

- **点击反馈动画**: overlay出现时的弹出动画
- **自动隐藏倒计时**: 顶部进度条显示剩余时间(15秒)
- **移动端适配**: 更小的尺寸和间距
- **点击态样式**: 元素点击时的视觉反馈

```scss
.mobile-mode {
  .element-overlays {
    &.mobile-clicked {
      animation: mobileOverlayShow 0.2s ease-out;
      border: 2px solid #18a058;
      
      // 自动隐藏倒计时进度条(15秒)
      &::after {
        animation: mobileOverlayTimer 15s linear;
      }
    }
  }
}
```

## 功能特性

### ✅ 移动端点击显示信息
- 点击BPMN节点显示详细信息
- 支持所有节点类型（开始事件、用户任务、服务任务、结束事件）

### ✅ 智能切换
- 点击同一节点：切换显示/隐藏
- 点击不同节点：切换到新节点信息
- 点击空白区域：隐藏当前信息

### ✅ 自动隐藏
- 15秒后自动隐藏信息框
- 顶部进度条显示剩余时间
- 可通过再次点击立即隐藏

### ✅ 视觉反馈
- 点击动画效果
- 移动端专用样式
- 更适合触摸操作的尺寸

### ✅ 性能优化
- 事件冲突解决，点击和拖拽分离
- 定时器自动清理防止内存泄漏
- 桌面端和移动端分离避免冗余

## 使用方法

1. **移动端访问**: 在移动设备上打开BPMN流程图
2. **点击节点**: 轻触任意流程节点查看详细信息
3. **切换信息**: 点击其他节点切换显示内容
4. **隐藏信息**: 点击同一节点或空白区域隐藏，或等待15秒自动隐藏

## 兼容性

- ✅ 保持桌面端原有功能不变
- ✅ 移动端和桌面端自动识别
- ✅ 支持所有现代移动浏览器
- ✅ 响应式设计适配不同屏幕尺寸
- ✅ 与原有拖拽、缩放功能无冲突

## 测试建议

1. 在移动设备上测试点击节点显示信息
2. 验证自动隐藏功能(15秒)
3. 测试多节点切换
4. 确认桌面端hover功能正常
5. 检查画布拖拽功能不受影响
6. 验证双指缩放功能正常 